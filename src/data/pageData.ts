import { PageData } from "../types/index";

// Single data entry point for all templates
export const getPageData = (): PageData => {
  return {
    title: "Welcome to Dynamic Build System",
    content:
      "Experience the power of dynamic React component rendering with multiple template support. Choose from different styling systems including Tailwind CSS, CSS Modules, and inline styles.",
    metadata: {
      description:
        "A dynamic build system for React components with multiple templates and styling approaches",
      keywords: ["React", "TypeScript", "Dynamic", "Templates", "Styling"],
    },
  };
};

// Additional page data for different sections if needed
export const getAboutPageData = (): PageData => {
  return {
    title: "About Dynamic Build System",
    content:
      "This system demonstrates how to build multiple template variations from a single codebase, each with different styling approaches but identical functionality.",
    metadata: {
      description: "Learn about the dynamic build system architecture",
      keywords: ["Architecture", "Build System", "Templates"],
    },
  };
};

export const getContactPageData = (): PageData => {
  return {
    title: "Contact Us",
    content:
      "Get in touch with us to learn more about implementing dynamic build systems for your projects.",
    metadata: {
      description: "Contact information for dynamic build system support",
      keywords: ["Contact", "Support", "Help"],
    },
  };
};
