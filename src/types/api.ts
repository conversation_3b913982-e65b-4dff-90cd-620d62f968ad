// Generic API response interface
export interface ApiResponse<T = any> {
  code: number;
  time: Date;
  msg: string;
  data: T;
  suffix: string;
}

// Type aliases for specific response types
export type BaseResponse = ApiResponse<Data>;
export type AdsResponse = ApiResponse<AdsData>;

// Generic response wrapper for simple API responses
export interface SimpleApiResponse<T = any> {
  status: string;
  data: T;
  message?: string;
}

export interface AdsData {
  ads: Ads;
  banners: Banner[];
}

export interface Ads {
  shouyehengfu: Shouyehengfu[];
}

export interface Shouyehengfu {
  img: string;
  url: string;
  name: string;
  status: number;
  local_img: string;
}

export interface Banner {
  img: string;
  url: string;
  title: string;
  type: number;
  local_img: string;
}

export interface Data {
  vip_list: List;
  topic_1_list: TopicList;
  recommend_list: List;
  topic_2_list: TopicList;
  commen_list: CommenList;
  topic_3_list: TopicList;
  zhongwen_list: List;
  yazhou_list: List;
  meinv_list: List;
  seo: SEO;
  notice: Notice;
}

export interface CommenList {
  data: CommenListDatum[];
}

export interface CommenListDatum {
  id: number;
  cat_id: number;
  fid: number;
  title: string;
  tags: DatumTags;
  status: number;
  status1: number;
  keywords: null;
  description: string;
  video_url: string;
  down_url: string;
  channel: DatumChannel;
  insert_time: number;
  update_time: number;
  from: number;
  file_name: string;
  actors: any[] | null;
  content: null;
  thumb: string;
  status2: number;
  publisher: string;
  series: string;
  is_top: number;
  publish_time: null;
  director: null;
  sub_tag: null;
  duration: number;
  maker: null;
  cover: string;
  collect_page: null;
  sync_bbs: number;
  thumb_series: string;
  thumb_ori: null;
  preview: string;
  watermark_state: number;
  reject_reason: null;
  sync_merchant: number;
  sync_yuhub: number;
}

export enum DatumChannel {
  Shipin = "shipin",
  Vip = "vip",
}

export enum DatumTags {
  中文字幕 = "中文字幕",
  亚洲无码 = "亚洲无码",
  制服淫穴 = "制服淫穴",
  国产精品 = "国产精品",
  成人动漫 = "成人动漫",
  换脸AI区 = "换脸AI区",
  猫咪推荐 = "猫咪推荐",
  美女主播 = "美女主播",
}

export interface List {
  data: CommenListDatum[];
  channel: DatumChannel;
  name: string;
}

export interface Notice {
  id: number;
  content: string;
  insert_time: number;
  update_time: number;
  status: number;
  title: string;
  sort: number;
  type: number;
  site: number;
  title_1: string;
  title_2: string;
  title_3: string;
  url_1: null;
  url_2: string;
  url_3: string;
  color_1: null;
  color_2: null;
  color_3: null;
  is_mobile: number;
  pic_1: null;
  pic_2: null;
  pic_3: null;
  display_mode: number;
  short_url_1: string;
  short_url_2: string;
  short_url_3: string;
  title_1_image: null;
  title_2_image: null;
  title_3_image: null;
}

export interface SEO {
  title: string;
  keywords: string;
  description: string;
}

export interface TopicList {
  data: Topic1_ListDatum[];
  channel: string;
}

export interface Topic1_ListDatum {
  id: number;
  title: string;
  status: number;
  list_status: number;
  videos_id: string;
  free_videos_id: string;
  insert_time: number;
  update_time: number;
  price: number;
  vip_price: number;
  file: string;
  desc: string;
  desc_images: null;
  gif_images: string;
  gallery: null[];
  tags: null | string;
  cover: string;
  phone_cover: string;
  phone_cover_365x250: string;
  list: ListElement[];
}

export interface ListElement {
  id: number;
  cat_id: number;
  fid: number;
  title: string;
  tags: ListTags;
  status: number;
  status1: number;
  keywords: null;
  description: string;
  video_url: string;
  down_url: string;
  channel: ListChannel;
  insert_time: number;
  update_time: number;
  from: number;
  file_name: string;
  actors: Actors;
  content: null;
  thumb: string;
  status2: number;
  publisher: string;
  series: string;
  is_top: number;
  publish_time: null;
  director: null;
  sub_tag: null;
  duration: number;
  maker: null;
  cover: string;
  collect_page: null;
  sync_bbs: number;
  thumb_series: string;
  thumb_ori: string;
  preview: string;
  watermark_state: number;
  reject_reason: null;
  sync_merchant: number;
  sync_yuhub: number;
}

export enum Actors {
  Empty = "[]",
}

export enum ListChannel {
  Remen = "remen",
}

export enum ListTags {
  其他 = "其他",
}
