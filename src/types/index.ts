export type BuildType = "1" | "2" | "3";

export type StylingSystem = "tailwind" | "css-modules" | "inline";

export interface BuildConfig {
  templateId: BuildType;
  stylingSystem: StylingSystem;
  title: string;
  description: string;
  theme?: {
    primaryColor?: string;
    secondaryColor?: string;
    fontFamily?: string;
  };
}

export interface PageData {
  title: string;
  content: string;
  metadata?: {
    description?: string;
    keywords?: string[];
  };
}

export type Page = "home" | "about";

export interface LayoutProps {
  children: React.ReactNode;
  config: BuildConfig;
  currentPage?: Page;
  onNavigate?: (page: Page) => void;
}

export interface HomepageProps {
  data: PageData;
  config: BuildConfig;
  onNavigate?: (page: Page) => void;
}

export interface PostDetailProps {
  id: string;
}

export interface VideoItem {
  id: number;
  title: string;
  code: string;
  performer: string;
  thumb: string;
  insetImages?: {
    left: string;
    right: string;
  };
  duration: number; // Changed to number (seconds)
  insert_time?: number; // Added insert_time
  isVip?: boolean;
  uploadTime?: string;
  overlayText?: {
    main: string;
    sub: string;
  };
  tags?: string;
}

export interface VideoList {
  name: string;
  data: VideoItem[];
}
