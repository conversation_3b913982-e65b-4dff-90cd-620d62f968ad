import axios, { type InternalAxiosRequestConfig } from "axios";
import CryptoJ<PERSON> from "crypto-js";

// Types for the request configuration
interface RequestConfig {
  __data?: any;
  __endpoint?: any;
  site?: boolean;
}

let cancelTokenSource = null as any;

export function cancelAllRequests() {
  if (cancelTokenSource) {
    cancelTokenSource = axios.CancelToken.source();
    cancelTokenSource.cancel();
  }
}

// Utility functions for encryption/decryption
const key = "SWRUSnEwSGtscHVJNm11OGlCJU9PQCF2ZF40SyZ1WFc=";
const iv = "JDB2QGtySDdWMg==";
// const sign_key = "JkI2OG1AJXpnMzJfJXUqdkhVbEU0V2tTJjFKNiUleG1VQGZO";
// const suffix = 123456;
// const secretKey = "D7hGKHnWThaECaQ3ji4XyAF3MfYKJ53M";
const enableEncryption = true;
const PROD_SITE = 1;

function base64decoder(str: string): string {
  return atob(str);
}

function decrypt(data: string, suffix: string = ""): string {
  try {
    let new_key_str = base64decoder(key);
    let new_iv_str = base64decoder(iv);
    let new_iv = CryptoJS.enc.Utf8.parse(new_iv_str + suffix);
    let new_key = CryptoJS.enc.Utf8.parse(new_key_str);

    let decrypted = CryptoJS.AES.decrypt(data, new_key, {
      iv: new_iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
      formatter: CryptoJS.format.OpenSSL,
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error("Decryption error:", error);
    return data; // Return original data if decryption fails
  }
}

// Request interceptor with comprehensive URL handling
axios.interceptors.request.use(
  (config: InternalAxiosRequestConfig<any> & RequestConfig) => {
    let isNeedSuffix = true;
    let isReqJson = false;

    // Get domain from environment or config
    let _domain = "https://mjson.szaction.cc/";
    if (_domain) {
      _domain = _domain.endsWith("/") ? _domain : `${_domain}/`;
    }

    // Time-based cache busting
    var time = new Date();
    if (time.getHours() < 4) {
      time.setHours(0, 0, 0, 0);
    } else if (time.getHours() < 8) {
      time.setHours(4, 0, 0, 0);
    } else if (time.getHours() < 12) {
      time.setHours(8, 0, 0, 0);
    } else if (time.getHours() < 16) {
      time.setHours(12, 0, 0, 0);
    } else if (time.getHours() < 20) {
      time.setHours(16, 0, 0, 0);
    } else if (time.getHours() < 24) {
      time.setHours(20, 0, 0, 0);
    }

    let ts = time.getTime().toString();

    const val = _domain;
    if (!val) return config;

    let _url = `/${config?.__endpoint}`;

    // Handle video detail endpoints
    if (_url && _url.indexOf("shipin/detail-") >= 0) {
      config.url = `${val}data${_url}.js?${ts}`;
      config.method = "get";
      isNeedSuffix = false;
    } else {
      config.url = `${val}data${_url}.js?${ts}`;
      config.method = "get";
      isNeedSuffix = false;
    }

    if (config.site) {
      config.url = `${val}data${_url}-${PROD_SITE}.js?${ts}`;
      config.method = "get";
      isNeedSuffix = false;
    }

    // Set headers based on suffix and JSON requirements
    if (isNeedSuffix) {
      config.headers["suffix"] = 123456;
    } else {
      delete config.headers["suffix"];
      config.headers["Content-Type"] = "text/plain";
    }

    if (isReqJson) {
      config.headers["Content-Type"] = "application/json";
    }

    return config;
  },
  (error) => {
    console.error("Request interceptor error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor for logging and data processing
axios.interceptors.response.use(
  (response) => {
    console.log("Response received:", response.status, response.config.url);
    console.log("Raw response data:", response.data);

    // Check if response needs decryption
    if (
      enableEncryption &&
      response.data &&
      response.data.data &&
      response.data.suffix
    ) {
      try {
        // Decrypt the response data
        let decryptedData = decrypt(response.data.data, response.data.suffix);
        decryptedData = JSON.parse(decryptedData);
        response.data.data = decryptedData;

        console.log("Decrypted response data:", response.data);
      } catch (error) {
        console.error("Failed to decrypt response:", error);
        // Keep original data if decryption fails
      }
    }

    return response;
  },
  (error) => {
    console.error("Response error:", error.response?.status, error.message);
    return Promise.reject(error);
  }
);

// Simple request function
export async function makeRequest(
  endpoint: string,
  options: {
    method?: "GET" | "POST" | "PUT" | "DELETE";
    data?: any;
    baseURL?: string;
    site?: boolean;
  } = {}
) {
  const { method = "POST", data, baseURL = "", site = false } = options;

  try {
    const response = await axios.request({
      url: endpoint,
      method,
      data,
      baseURL,
      __endpoint: endpoint.replace(/^\//, ""), // Remove leading slash for endpoint
      site,
    } as any);

    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${endpoint}:`, error);
    throw error;
  }
}
