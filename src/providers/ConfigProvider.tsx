import React, { createContext, useContext, ReactNode } from "react";
import { useConfigBase } from "../hooks/useApi";

interface ConfigContextType {
  config: any; // API config data
  isLoading: boolean;
  error: any;
}

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

interface ConfigProviderProps {
  children: ReactNode;
}

export const ConfigProvider: React.FC<ConfigProviderProps> = ({ children }) => {
  const { data: configData, isLoading, error } = useConfigBase();

  // Show loading state while config is loading
  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "100vh",
          backgroundColor: "#f9fafb",
        }}
      >
        <div
          style={{
            width: "48px",
            height: "48px",
            border: "4px solid #e5e7eb",
            borderTop: "4px solid #10B981",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
            marginBottom: "16px",
          }}
        />
        <p style={{ color: "#6b7280", fontSize: "16px", margin: 0 }}>
          Loading configuration...
        </p>
        <style
          dangerouslySetInnerHTML={{
            __html: `
            @keyframes spin {
              from { transform: rotate(0deg); }
              to { transform: rotate(360deg); }
            }
          `,
          }}
        />
      </div>
    );
  }

  // Show error state if config fails to load
  if (error) {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "100vh",
          backgroundColor: "#fef2f2",
          padding: "24px",
        }}
      >
        <div
          style={{
            backgroundColor: "white",
            padding: "24px",
            borderRadius: "8px",
            boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
            textAlign: "center",
            maxWidth: "400px",
          }}
        >
          <h2 style={{ color: "#dc2626", marginBottom: "16px" }}>
            Configuration Error
          </h2>
          <p style={{ color: "#6b7280", marginBottom: "16px" }}>
            Failed to load application configuration: {error.message}
          </p>
          <button
            onClick={() => window.location.reload()}
            style={{
              backgroundColor: "#10B981",
              color: "white",
              padding: "8px 16px",
              borderRadius: "4px",
              border: "none",
              cursor: "pointer",
            }}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Only render children when config is successfully loaded
  return (
    <ConfigContext.Provider value={{ config: configData, isLoading, error }}>
      {children}
    </ConfigContext.Provider>
  );
};

export const useConfig = (): ConfigContextType => {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error("useConfig must be used within a ConfigProvider");
  }
  return context;
};
