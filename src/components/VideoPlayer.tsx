import { useEffect, useRef, useState } from "react";
import { processVideoUrl } from "../utils/video";
import { useConfig } from "../providers/ConfigProvider";
import Hls from "hls.js";

// DPlayer types
declare global {
  interface Window {
    DPlayer: any;
  }
}

interface VideoPlayerProps {
  videoUrl: string;
  source?: string;
  poster?: string;
  className?: string;
  onReady?: (player: any) => void;
  onError?: (error: any) => void;
}

export default function VideoPlayer({
  videoUrl,
  source,
  poster,
  className = "",
  onReady,
  onError,
}: VideoPlayerProps) {
  const { config: configData } = useConfig();
  const config = configData?.data;
  const containerRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<any>(null);
  const hlsRef = useRef<Hls | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [finalSource, setFinalSource] = useState<string | null>(null);

  useEffect(() => {
    console.log("🔧 VideoPlayer: Setting up final source", config);
    console.log("  Config m3u8_host_encrypt:", config?.m3u8_host_encrypt);
    console.log("  Config m3u8_host_encrypt1:", config?.m3u8_host_encrypt1);
    console.log("  Config m3u8_host_encrypt2:", config?.m3u8_host_encrypt2);
    console.log("  Source prop:", source);

    if (config?.m3u8_host_encrypt) {
      console.log(
        "  ✅ Using config m3u8_host_encrypt as final source:",
        config.m3u8_host_encrypt
      );
      setFinalSource(config.m3u8_host_encrypt);
    } else if (source) {
      console.log("  ✅ Using source prop as final source:", source);
      setFinalSource(source);
    } else {
      console.log("  ❌ No source available");
      setFinalSource(null);
    }
  }, [source, config]);

  useEffect(() => {
    console.log("🔧 finalSource", finalSource);
    console.log("  videoUrl", videoUrl);
    if (!videoUrl || !containerRef.current || !finalSource) return;

    // Clean up previous player
    if (playerRef.current) {
      try {
        playerRef.current.destroy();
      } catch (err) {
        console.warn("Error destroying previous player:", err);
      }
      playerRef.current = null;
    }

    // Load DPlayer if not already loaded
    const loadDPlayer = async () => {
      try {
        // Check if DPlayer is already available
        if (!window.DPlayer) {
          // Dynamically import DPlayer
          const DPlayer = await import("dplayer");
          window.DPlayer = DPlayer.default || DPlayer;
        }

        // Step 2: Process video URL following original implementation
        const {
          fullUrl,
          finalSource: processedSource,
          finalPath,
        } = processVideoUrl(videoUrl, finalSource, config);

        // Debug logging for video source
        console.log("🎥 VideoPlayer Debug Info:");
        console.log("  Original videoUrl prop:", videoUrl);
        console.log("  Input source host:", finalSource);
        console.log("  Config:", config);
        console.log("  Processed finalSource:", processedSource);
        console.log("  Processed finalPath:", finalPath);
        console.log("  Final video URL:", fullUrl);

        // Step 3: DPlayer Initialization with custom HLS handling
        const isHLS = fullUrl.includes(".m3u8");
        console.log("  Is HLS stream:", isHLS);
        const playerOptions = {
          container: containerRef.current,
          video: {
            url: isHLS ? "" : fullUrl, // Empty URL for HLS, we'll handle it manually
            type: isHLS ? "customHls" : "auto",
            defaultQuality: 0,
          },
          hotkey: true,
          autoplay: false,
          preload: "metadata",
          mutex: true,
          theme: "#007bff",
        };

        let player: any;
        try {
          player = new window.DPlayer(playerOptions);
        } catch (initError) {
          const errorMessage =
            initError instanceof Error
              ? initError.message
              : "Unknown initialization error";
          setError(`Failed to initialize video player: ${errorMessage}`);
          setIsLoading(false);
          return;
        }

        // Step 4: HLS.js Integration for .m3u8 files
        if (isHLS && Hls.isSupported()) {
          console.log("🔧 Setting up HLS.js for m3u8 stream");
          console.log("  HLS.js supported:", Hls.isSupported());

          // Clean up previous HLS instance
          if (hlsRef.current) {
            console.log("  Destroying previous HLS instance");
            hlsRef.current.destroy();
          }

          const hls = new Hls({
            enableWorker: true,
            lowLatencyMode: false,
          });

          hlsRef.current = hls;

          // Get the video element from DPlayer
          const videoElement = player.video;
          console.log("  DPlayer video element:", videoElement);
          console.log("  Loading HLS source:", fullUrl);

          // Load the HLS source
          hls.loadSource(fullUrl);
          hls.attachMedia(videoElement);

          // HLS event listeners
          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            console.log("✅ HLS manifest parsed successfully");
            console.log("  Available levels:", hls.levels);
            setIsLoading(false);
            onReady?.(player);
          });

          hls.on(Hls.Events.ERROR, (event, data) => {
            console.error("❌ HLS error:", event, data);
            console.error("  Error type:", data.type);
            console.error("  Error details:", data.details);
            console.error("  Error fatal:", data.fatal);
            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  console.error("💥 Fatal network error encountered");
                  setError("Network error loading video stream");
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  console.error("💥 Fatal media error encountered");
                  setError("Media error in video stream");
                  break;
                default:
                  console.error("💥 Fatal error, cannot recover");
                  setError(`HLS streaming error: ${data.details}`);
                  break;
              }
              setIsLoading(false);
              onError?.(data);
            }
          });

          hls.on(Hls.Events.LEVEL_LOADED, (_, data) => {
            console.log("📊 HLS level loaded:", data.level);
          });
        } else if (isHLS && !Hls.isSupported()) {
          console.warn("⚠️ HLS.js not supported, falling back to native HLS");
          console.log("  Browser HLS support check...");
          // Fallback to native HLS support
          const videoElement = player.video;
          const hlsSupport = videoElement.canPlayType(
            "application/vnd.apple.mpegurl"
          );
          console.log("  Native HLS support:", hlsSupport);
          if (hlsSupport) {
            videoElement.src = fullUrl;
            console.log("✅ Using native HLS support with URL:", fullUrl);
          } else {
            console.error("❌ HLS streaming not supported in this browser");
            setError("HLS streaming not supported in this browser");
            setIsLoading(false);
            return;
          }
        } else if (!isHLS) {
          console.log("📹 Non-HLS video, using direct URL:", fullUrl);
        }

        player.on("loadstart", () => {
          console.log("🔄 DPlayer: Video load started");
          setIsLoading(true);
          setError(null);
        });

        player.on("canplay", () => {
          console.log("✅ DPlayer: Video can play");
          setIsLoading(false);
          onReady?.(player);
        });

        player.on("error", (err: any) => {
          console.error("❌ DPlayer error:", err);
          setError("Failed to load video");
          setIsLoading(false);
          onError?.(err);
        });

        playerRef.current = player;
      } catch (err) {
        setError("Failed to initialize video player");
        setIsLoading(false);
        onError?.(err);
      }
    };

    loadDPlayer();

    // Cleanup on unmount
    return () => {
      if (playerRef.current) {
        try {
          playerRef.current.destroy();
        } catch (err) {
          console.warn("Error destroying player on cleanup:", err);
        }
        playerRef.current = null;
      }
    };
  }, [videoUrl, finalSource, poster, config, onReady, onError]);

  if (error) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-900 text-white rounded-lg ${className}`}
      >
        <div className="text-center p-8">
          <div className="text-red-500 mb-4">
            <svg
              className="w-16 h-16 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-2">Video Error</h3>
          <p className="text-gray-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white rounded-lg z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading video...</p>
          </div>
        </div>
      )}
      <div
        ref={containerRef}
        className="w-full h-full rounded-lg overflow-hidden"
        style={{ minHeight: "300px" }}
      />
    </div>
  );
}

// Hook for easier usage
export function useVideoPlayer() {
  const [player, setPlayer] = useState<any>(null);

  const handleReady = (playerInstance: any) => {
    setPlayer(playerInstance);
  };

  const play = () => player?.play();
  const pause = () => player?.pause();
  const seek = (time: number) => player?.seek(time);
  const volume = (vol: number) => player?.volume(vol);
  const destroy = () => {
    player?.destroy();
    setPlayer(null);
  };

  return {
    player,
    handleReady,
    controls: {
      play,
      pause,
      seek,
      volume,
      destroy,
    },
  };
}
