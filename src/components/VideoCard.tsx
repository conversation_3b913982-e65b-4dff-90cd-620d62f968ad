import React from "react";
import { useNavigate } from "react-router-dom";
import Image from "./Image";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import "dayjs/locale/zh-cn";

// Configure dayjs
dayjs.extend(relativeTime);
dayjs.locale("zh-cn");

interface VideoCardProps {
  id: number;
  title: string;
  performer: string;
  thumb: string;
  insetImages?: {
    left: string;
    right: string;
  };
  duration: number; // Changed to number (seconds)
  insert_time?: number; // Added insert_time
  isVip?: boolean;
  uploadTime?: string;
  overlayText?: {
    main: string;
    sub: string;
  };
  className?: string;
  style?: React.CSSProperties;
}

// Helper function to format duration from seconds to h:m:s
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }
};

const VideoCard: React.FC<VideoCardProps> = ({
  id,
  title,
  performer,
  thumb,
  insetImages,
  duration,
  insert_time,
  isVip = false,
  uploadTime,
  overlayText,
  className,
  style,
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/post-detail/${id}`);
  };
  return (
    <div
      className={`relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer ${className}`}
      style={{
        backgroundColor: "white",
        borderRadius: "0.5rem",
        overflow: "hidden",
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
        transition: "box-shadow 0.2s ease-in-out",
        cursor: "pointer",
        ...style,
      }}
      onClick={handleClick}
    >
      {/* Main thumbnail container */}
      <div
        className="relative w-full"
        style={{
          position: "relative",
          width: "100%",
          paddingBottom: "56.25%", // 16:9 aspect ratio
          overflow: "hidden",
        }}
      >
        {/* Main thumbnail */}
        <Image
          src={thumb}
          alt={title}
          className="absolute inset-0 w-full h-full object-cover"
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            objectFit: "cover",
          }}
          width={400}
          height={225}
          loading="lazy"
        />

        {/* VIP Badge */}
        {isVip && (
          <div
            className="absolute top-2 right-2 bg-yellow-500 text-black px-2 py-1 rounded text-xs font-bold"
            style={{
              position: "absolute",
              top: "0.5rem",
              right: "0.5rem",
              backgroundColor: "#eab308",
              color: "#000000",
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              fontSize: "0.75rem",
              fontWeight: "bold",
            }}
          >
            VIP
          </div>
        )}

        {/* Inset Images */}
        {insetImages && (
          <>
            {/* Left inset */}
            <div
              className="absolute top-2 left-2 w-12 h-8 rounded overflow-hidden"
              style={{
                position: "absolute",
                top: "0.5rem",
                left: "0.5rem",
                width: "3rem",
                height: "2rem",
                borderRadius: "0.25rem",
                overflow: "hidden",
              }}
            >
              <Image
                src={insetImages.left}
                alt="Left inset"
                className="w-full h-full object-cover"
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                }}
                width={48}
                height={32}
                loading="lazy"
              />
            </div>

            {/* Right inset */}
            <div
              className="absolute top-2 right-12 w-12 h-8 rounded overflow-hidden"
              style={{
                position: "absolute",
                top: "0.5rem",
                right: "3rem",
                width: "3rem",
                height: "2rem",
                borderRadius: "0.25rem",
                overflow: "hidden",
              }}
            >
              <Image
                src={insetImages.right}
                alt="Right inset"
                className="w-full h-full object-cover"
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                }}
                width={48}
                height={32}
                loading="lazy"
              />
            </div>
          </>
        )}

        {/* Duration overlay */}
        <div
          className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs"
          style={{
            position: "absolute",
            bottom: "0.5rem",
            left: "0.5rem",
            backgroundColor: "rgba(0, 0, 0, 0.75)",
            color: "white",
            padding: "0.25rem 0.5rem",
            borderRadius: "0.25rem",
            fontSize: "0.75rem",
          }}
        >
          {formatDuration(duration)}
        </div>

        {/* Insert time overlay (bottom right) */}
        {insert_time && (
          <div
            className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs"
            style={{
              position: "absolute",
              bottom: "0.5rem",
              right: "0.5rem",
              backgroundColor: "rgba(0, 0, 0, 0.75)",
              color: "white",
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              fontSize: "0.75rem",
            }}
          >
            {dayjs.unix(insert_time).fromNow()}
          </div>
        )}

        {/* Text overlay */}
        {overlayText && (
          <div
            className="absolute bottom-2 right-2 text-right"
            style={{
              position: "absolute",
              bottom: "0.5rem",
              right: "0.5rem",
              textAlign: "right",
            }}
          >
            <div
              className="text-yellow-400 text-sm font-medium"
              style={{
                color: "#fbbf24",
                fontSize: "0.875rem",
                fontWeight: "500",
              }}
            >
              {overlayText.main}
            </div>
            <div
              className="text-white text-xs"
              style={{
                color: "white",
                fontSize: "0.75rem",
              }}
            >
              {overlayText.sub}
            </div>
            {uploadTime && (
              <div
                className="text-white text-xs mt-1"
                style={{
                  color: "white",
                  fontSize: "0.75rem",
                  marginTop: "0.25rem",
                }}
              >
                {uploadTime}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Video info below thumbnail */}
      <div
        className="p-3"
        style={{
          padding: "0.75rem",
        }}
      >
        {/* Video code and title */}
        <h3
          className="font-semibold text-gray-900 mb-1 line-clamp-2"
          style={{
            fontWeight: "600",
            color: "#111827",
            marginBottom: "0.25rem",
            display: "-webkit-box",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            fontSize: "0.875rem",
          }}
        >
          {title}
        </h3>

        {/* Performer name */}
        <p
          className="text-sm text-gray-600"
          style={{
            fontSize: "0.875rem",
            color: "#4b5563",
          }}
        >
          {performer}
        </p>
      </div>
    </div>
  );
};

export default VideoCard;
