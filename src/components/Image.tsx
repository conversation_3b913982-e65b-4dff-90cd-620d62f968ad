import React, {
  useState,
  useCallback,
  useMemo,
  useRef,
  type CSSProperties,
} from "react";
import { useConfig } from "../providers/ConfigProvider";

// You'll need to import these utilities from your existing codebase
// import u from "@/util";
// For now, I'll create placeholder functions - replace with your actual imports
const u = {
  fetchData: async (url: string) => {
    const response = await fetch(url);
    return response.text();
  },
  imgDecrypt: (data: string) => {
    // Replace with your actual decryption logic
    return data;
  },
};

// Utility function for appending host to image URLs
const appendHost = (
  url: string,
  domainSourceUrl: string = "",
  withBuild: boolean = true,
  size: string = "50x50"
) => {
  let imgDomain = `${domainSourceUrl}`;

  if (!url) return "";

  // Ensure the URL does not start with the domain
  if (url.startsWith(imgDomain)) {
    // Remove the domain from the URL if it is already included
    url = url.replace(imgDomain, "");
  }

  // Avoid appending `.txt?size=50x50` multiple times
  if (!url.endsWith(`.txt?size=${size}`)) {
    if (url.endsWith(".txt")) {
      // If it ends with `.txt`, just add the query parameter
      url = `${url}?size=${size}`;
    } else {
      // If it does not end with `.txt`, append both `.txt` and the query parameter
      url = `${url}.txt?size=${size}`;
    }
  }

  if (!withBuild) {
    return `${imgDomain.replace(`/build1`, "")}${url}`;
  }
  return `${imgDomain}${url}`;
};

// Helper function to determine size from width and height props
const getSizeFromDimensions = (
  width?: number | string,
  height?: number | string
): string => {
  // Convert to numbers if they're strings
  const w = typeof width === "string" ? parseInt(width) : width;
  const h = typeof height === "string" ? parseInt(height) : height;

  // If both width and height are provided, use them
  if (w && h) {
    return `${w}x${h}`;
  }

  // If only width is provided, make it square
  if (w) {
    return `${w}x${w}`;
  }

  // If only height is provided, make it square
  if (h) {
    return `${h}x${h}`;
  }

  // Default size
  return "50x50";
};

interface ImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: CSSProperties;
  id?: string;
  onClick?: () => void;
  errorImg?: string;
  loading?: "lazy" | "eager";
  children?: (src: string) => React.ReactNode;
  // Additional props for advanced image processing
  getFullImgUrl?: boolean;
  withoutSize?: boolean;
  errorCheck?: boolean;
}

const Image: React.FC<ImageProps> = ({
  src,
  alt,
  width,
  height,
  className = "",
  style,
  id,
  onClick,
  errorImg,
  loading = "lazy",
  children,
  getFullImgUrl,
  withoutSize,
  errorCheck,
}) => {
  const { config: configData } = useConfig();
  const [error, setError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [retryCount, setRetryCount] = useState<number>(0);
  const imgRef = useRef<HTMLImageElement>(null);
  const maxRetries = 2; // Limit retries to prevent infinite loops

  // Get image domain from config at component level
  const imageDomain = configData?.data?.mm_web_image_domain;

  const errorPlaceholder =
    errorImg ||
    `data:image/svg+xml;base64,${btoa(`
    <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <style>
          .skeleton {
            animation: pulse 1.5s ease-in-out infinite;
          }
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }
        </style>
      </defs>
      <rect width="100%" height="100%" fill="#e5e7eb" class="skeleton"/>
    </svg>
  `)}`;

  // Simple function to get the final image source
  const getFinalSrc = () => {
    // Handle direct URLs
    if (getFullImgUrl && src) {
      return src;
    }

    // Handle empty src
    if (!src) {
      return errorPlaceholder;
    }

    // Skip certain URLs immediately to prevent infinite loops

    // Handle wsSecret URLs directly
    if (!src.includes("loading") && src.includes("wsSecret")) {
      return src;
    }

    // Handle base64 images directly
    if (src.indexOf("data:") >= 0) {
      return src;
    }

    // Handle HTTP/HTTPS URLs directly
    if (src.startsWith("http://") || src.startsWith("https://")) {
      return src;
    }

    // For relative URLs, process with image domain if available
    if (!src.startsWith("http") && imageDomain) {
      const imageSize = getSizeFromDimensions(width, height);

      if (!src.includes(imageDomain)) {
        const processedSrc = appendHost(src, imageDomain, true, imageSize);
        return processedSrc;
      }
    }

    // For encrypted URLs, start with the original src and let onError handle processing
    return src;
  };

  const finalSrc = getFinalSrc();

  // Reset retry count when src changes
  React.useEffect(() => {
    setRetryCount(0);
    setError(false);
    setIsLoading(true);
  }, [src]);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setError(false);
    setRetryCount(0); // Reset retry count on successful load
  }, []);

  const handleError = useCallback(async () => {
    // If it's already an error placeholder, don't retry
    if (finalSrc === errorPlaceholder) {
      setError(true);
      setIsLoading(false);
      return;
    }

    // Check retry limit to prevent infinite loops
    if (retryCount >= maxRetries) {
      console.warn(`Max retries (${maxRetries}) reached for image:`, src);
      setError(true);
      setIsLoading(false);
      return;
    }

    // Increment retry count
    setRetryCount((prev) => prev + 1);

    // Try to process image with domain processing if it's not already processed
    if (!src.startsWith("http") && imageDomain) {
      try {
        setIsLoading(true);

        // Use finalSrc which already has domain processing applied
        let processedSrc = finalSrc;

        if (processedSrc?.includes("wsSecret")) {
          if (imgRef.current) {
            imgRef.current.src = processedSrc;
          }
          return;
        }

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Image fetch timeout")), 10000); // 10 second timeout
        });

        const res = (await Promise.race([
          u.fetchData(
            processedSrc
              .replace(/\/\//g, "/")
              .replace(/https:\//g, "https://")
              .replace(/http:\//g, "http://")
          ),
          timeoutPromise,
        ])) as string;

        if (res && typeof res === "string") {
          if (res.includes("404 Not Found")) {
            setError(true);
          } else {
            const decodedImg =
              res.indexOf("data") >= 0 ? res : u.imgDecrypt(res);
            if (imgRef.current) {
              imgRef.current.src = decodedImg;
            }
            setError(false);
          }
        } else {
          setError(true);
        }
      } catch (error) {
        console.error("Error processing image:", error);
        setError(true);
      } finally {
        setIsLoading(false);
      }
    } else {
      setError(true);
      setIsLoading(false);
    }
  }, [
    src,
    configData,
    imageDomain,
    width,
    height,
    withoutSize,
    finalSrc,
    errorPlaceholder,
    retryCount,
    maxRetries,
  ]);

  const computedStyle: CSSProperties = useMemo(() => {
    const baseStyle: CSSProperties = {
      ...style,
    };

    if (width) {
      baseStyle.width = typeof width === "number" ? `${width}px` : width;
    }
    if (height) {
      baseStyle.height = typeof height === "number" ? `${height}px` : height;
    }

    return baseStyle;
  }, [style, width, height, isLoading]);

  const imageElement = useMemo(
    () => (
      <img
        ref={imgRef}
        src={error ? errorPlaceholder : finalSrc}
        alt={alt}
        className={`${className}`}
        style={computedStyle}
        id={id}
        onClick={onClick}
        onLoad={handleLoad}
        onError={handleError}
        loading={loading}
      />
    ),
    [
      error,
      errorPlaceholder,
      finalSrc,
      alt,
      className,
      isLoading,
      computedStyle,
      id,
      onClick,
      handleLoad,
      handleError,
      loading,
    ]
  );

  return (
    <>
      {errorCheck ? (error ? null : imageElement) : imageElement}
      {children && children(error ? errorPlaceholder : finalSrc)}
    </>
  );
};

export default Image;
