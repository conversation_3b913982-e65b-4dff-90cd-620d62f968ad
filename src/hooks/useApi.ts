// React Query hooks for API requests
import { useQuery, type UseQueryOptions } from "@tanstack/react-query";
import { makeRequest } from "../api/request";
import { BaseResponse, AdsResponse, SimpleApiResponse } from "../types/api";
import { processHomeData } from "../utils/dataProcessor";

// Query keys factory for consistent cache management
export const apiQueryKeys = {
  all: ["api"] as const,

  // Home endpoint
  home: () => [...apiQueryKeys.all, "home"] as const,

  // Config endpoint
  configBase: () => [...apiQueryKeys.all, "config", "base"] as const,

  // Video detail endpoint
  videoDetail: (id: string) =>
    [...apiQueryKeys.all, "shipin", "detail", id] as const,

  // Ads endpoint
  ads: () => [...apiQueryKeys.all, "ads"] as const,
};

// Re-export the generic types from api.ts for convenience
export type { SimpleApiResponse } from "../types/api";

// Home endpoint hooks
export function useHomeData(
  options?: Omit<UseQueryOptions<BaseResponse, Error>, "queryKey" | "queryFn">
) {
  return useQuery({
    queryKey: apiQueryKeys.home(),
    queryFn: async () => {
      const rawData = await makeRequest("/index/home", { method: "GET" });
      return processHomeData(rawData);
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    ...options,
  });
}

// Config base endpoint hooks
export function useConfigBase(
  options?: Omit<
    UseQueryOptions<SimpleApiResponse, Error>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: apiQueryKeys.configBase(),
    queryFn: () => makeRequest("/config/base", { method: "GET", site: true }),
    staleTime: 1000 * 60 * 10, // 10 minutes (config data changes less frequently)
    ...options,
  });
}

// Video detail endpoint hooks
export function useVideoDetail(
  id: string,
  options?: Omit<
    UseQueryOptions<SimpleApiResponse, Error>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: apiQueryKeys.videoDetail(id),
    queryFn: () => makeRequest(`/shipin/detail-${id}`, { method: "GET" }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    enabled: !!id, // Only fetch when id is provided
    ...options,
  });
}

// Ads endpoint hooks
export function useAdsData(
  options?: Omit<UseQueryOptions<AdsResponse, Error>, "queryKey" | "queryFn">
) {
  return useQuery({
    queryKey: apiQueryKeys.ads(),
    queryFn: () => makeRequest("/material/index", { method: "GET" }),
    staleTime: 1000 * 60 * 10, // 10 minutes (ads data changes less frequently)
    ...options,
  });
}
