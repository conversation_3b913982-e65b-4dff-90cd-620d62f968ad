import { BuildConfig, BuildType } from "../types/index";

// Build configuration mapping - All templates use the same UI design with different styling approaches
const buildConfigs: Record<BuildType, BuildConfig> = {
  "1": {
    templateId: "1",
    stylingSystem: "tailwind",
    title: "Classic Tailwind Template",
    description: "A classic design using Tailwind CSS",
    theme: {
      primaryColor: "#10B981",
      secondaryColor: "#059669",
      fontFamily: "Georgia, serif",
    },
  },
  "2": {
    templateId: "2",
    stylingSystem: "css-modules",
    title: "Classic CSS Modules Template",
    description: "A classic design using CSS Modules",
    theme: {
      primaryColor: "#10B981",
      secondaryColor: "#059669",
      fontFamily: "Georgia, serif",
    },
  },
  "3": {
    templateId: "3",
    stylingSystem: "inline",
    title: "Classic Inline Styles Template",
    description: "A classic design using inline styles",
    theme: {
      primaryColor: "#10B981",
      secondaryColor: "#059669",
      fontFamily: "Georgia, serif",
    },
  },
};

export const getBuildConfig = (buildType: BuildType): BuildConfig => {
  return buildConfigs[buildType];
};

export const getAvailableBuildTypes = (): BuildType[] => {
  return Object.keys(buildConfigs) as BuildType[];
};
