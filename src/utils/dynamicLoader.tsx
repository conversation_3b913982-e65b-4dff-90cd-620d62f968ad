import React, { Suspense, ComponentType } from "react";
import { BuildType } from "../types/index";

// Static imports for production builds - only existing components
import Layout1 from "../templates/1/layout";
import Homepage1 from "../templates/1/homepage";
import Postdetail1 from "../templates/1/postdetail";

import Layout2 from "../templates/2/layout";
import Homepage2 from "../templates/2/homepage";
import Postdetail2 from "../templates/2/postdetail";

import Layout3 from "../templates/3/layout";
import Homepage3 from "../templates/3/homepage";
import Postdetail3 from "../templates/3/postdetail";

import Layout4 from "../templates/4/layout";
import Homepage4 from "../templates/4/homepage";
import Postdetail4 from "../templates/4/postdetail";

import Layout5 from "../templates/5/layout";
import Homepage5 from "../templates/5/homepage";
import Postdetail5 from "../templates/5/postdetail";

import Layout6 from "../templates/6/layout";
import Homepage6 from "../templates/6/homepage";
import Postdetail6 from "../templates/6/postdetail";

// Static component map for reliable production builds
const componentMap: Record<string, ComponentType<any>> = {
  "1-layout": Layout1,
  "1-homepage": Homepage1,
  "1-postdetail": Postdetail1,

  "2-layout": Layout2,
  "2-homepage": Homepage2,
  "2-postdetail": Postdetail2,

  "3-layout": Layout3,
  "3-homepage": Homepage3,
  "3-postdetail": Postdetail3,
  "4-layout": Layout4,
  "4-homepage": Homepage4,
  "4-postdetail": Postdetail4,
  "5-layout": Layout5,
  "5-homepage": Homepage5,
  "5-postdetail": Postdetail5,
  "6-layout": Layout6,
  "6-homepage": Homepage6,
  "6-postdetail": Postdetail6,
};

// Cache for loaded components to avoid re-importing
const componentCache = new Map<string, ComponentType<any>>();

// Enhanced template loader with static fallback for production
export const loadTemplate = (templateId: BuildType, componentName: string) => {
  const cacheKey = `${templateId}-${componentName}`;

  // Return cached component if available
  if (componentCache.has(cacheKey)) {
    return componentCache.get(cacheKey)!;
  }

  // Try static component map first (for production builds)
  const staticComponent = componentMap[cacheKey];
  if (staticComponent) {
    componentCache.set(cacheKey, staticComponent);
    return staticComponent;
  }

  // Fallback to template 1 if specific template not found
  const fallbackKey = `1-${componentName}`;
  const fallbackComponent = componentMap[fallbackKey];
  if (fallbackComponent) {
    console.warn(
      `Template ${templateId}/${componentName} not found, using fallback template 1`
    );
    componentCache.set(cacheKey, fallbackComponent);
    return fallbackComponent;
  }

  // If all else fails, return error component
  const ErrorComponent = ({ ...props }: any) => (
    <div
      style={{
        padding: "20px",
        textAlign: "center",
        color: "red",
        border: "1px solid red",
        borderRadius: "4px",
        margin: "10px",
      }}
    >
      <h3>Component Not Found</h3>
      <p>
        Template {templateId}/{componentName} could not be loaded
      </p>
      <details style={{ marginTop: "10px", textAlign: "left" }}>
        <summary>Debug Info</summary>
        <pre style={{ fontSize: "12px", color: "#666" }}>
          {JSON.stringify(
            {
              templateId,
              componentName,
              props,
              availableComponents: Object.keys(componentMap),
            },
            null,
            2
          )}
        </pre>
      </details>
    </div>
  );

  componentCache.set(cacheKey, ErrorComponent);
  return ErrorComponent;
};

interface DynamicComponentProps {
  templateId: BuildType;
  componentName: string; // Made flexible to accept any component name
  props?: any;
  fallback?: React.ReactNode;
  children?: React.ReactNode;
}

export const DynamicComponent: React.FC<DynamicComponentProps> = ({
  templateId,
  componentName,
  props = {},
  fallback = (
    <div
      style={{
        padding: "20px",
        textAlign: "center",
        color: "#666",
        fontStyle: "italic",
      }}
    >
      Loading {componentName} template {templateId}...
    </div>
  ),
  children,
}) => {
  const Component = loadTemplate(templateId, componentName);

  return (
    <Suspense fallback={fallback}>
      <Component {...props}>{children}</Component>
    </Suspense>
  );
};

// Utility functions for template management

/**
 * Get all available template IDs
 */
export const getAvailableTemplates = (): string[] => {
  const templates = new Set<string>();
  Object.keys(componentMap).forEach((key) => {
    const templateId = key.split("-")[0];
    templates.add(templateId);
  });
  return Array.from(templates);
};

/**
 * Get all available component names for a template
 */
export const getAvailableComponents = (templateId: BuildType): string[] => {
  const components = new Set<string>();
  Object.keys(componentMap).forEach((key) => {
    if (key.startsWith(`${templateId}-`)) {
      const componentName = key.split("-")[1];
      components.add(componentName);
    }
  });
  return Array.from(components);
};

/**
 * Check if a template and component combination exists
 */
export const templateExists = (
  templateId: BuildType,
  componentName: string
): boolean => {
  const key = `${templateId}-${componentName}`;
  return key in componentMap;
};

/**
 * Preload a template component for better performance
 */
export const preloadTemplate = (
  templateId: BuildType,
  componentName: string
): void => {
  // Load the component into cache
  loadTemplate(templateId, componentName);
};

/**
 * Preload all components for a template
 */
export const preloadAllComponents = (templateId: BuildType): void => {
  const components = getAvailableComponents(templateId);
  components.forEach((componentName) => {
    preloadTemplate(templateId, componentName);
  });
};

/**
 * Clear the component cache
 */
export const clearComponentCache = (): void => {
  componentCache.clear();
};

/**
 * Get cache statistics
 */
export const getCacheStats = () => {
  return {
    size: componentCache.size,
    keys: Array.from(componentCache.keys()),
  };
};
