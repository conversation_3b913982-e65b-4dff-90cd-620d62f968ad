import { BaseResponse } from "../types/api";

/**
 * Processes home data by removing seo/notice and adding channel to commen_list
 * This matches the old project's data processing logic
 */
export function processHomeData(data: BaseResponse): BaseResponse {
  const _newdata = { ...data };

  // Remove seo and notice from data
  delete (_newdata.data as any).seo;
  delete (_newdata.data as any).notice;

  // Add channel to commen_list
  if (_newdata.data.commen_list) {
    (_newdata.data.commen_list as any).channel = "shipin";
  }

  return _newdata;
}
