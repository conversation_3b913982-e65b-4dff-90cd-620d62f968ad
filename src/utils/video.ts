import CryptoJS from "crypto-js";

const secretKey = "D7hGKHnWThaECaQ3ji4XyAF3MfYKJ53M";

/**
 * Add video key parameters for secure video streaming
 * @param url - The video URL path
 * @returns Query string with wsSecret and wsTime parameters
 */
export function addVidKeyParam(url: string): string {
  const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
  const storedTime = sessionStorage.getItem("timestamp"); // Get the stored timestamp from sessionStorage

  let time: number; // Declare the time variable

  // If there is a stored time and 5 minutes haven't passed, reuse the stored time
  if (storedTime && currentTime - parseInt(storedTime) < 300) {
    time = parseInt(storedTime) + 300; // Use the stored timestamp and add 300 seconds
  } else {
    // If no stored time or more than 5 minutes have passed, generate a new timestamp
    time = currentTime + 300; // Add 300 seconds to the current time
    sessionStorage.setItem("timestamp", currentTime.toString()); // Store the new timestamp in sessionStorage
  }

  // The URI for which we're generating the key
  const uri = url;

  // Concatenate secret_key, uri, and time (decimal) for the MD5 hash
  const paramToAppend = secretKey + uri + time; // Time in decimal

  // Calculate the MD5 hash using CryptoJS
  const key = CryptoJS.MD5(paramToAppend).toString();

  // Return the URL with wsSecret (the MD5 hash) and wsTime (in decimal)
  return "?wsSecret=" + key + "&wsTime=" + time; // wsTime is the time in seconds (decimal)
}

/**
 * Process video URL and source to create the final streaming URL
 * @param vidurl - The video URL path
 * @param source - The source host/path
 * @param config - Configuration object with m3u8_host_encrypt
 * @returns Object with finalSource and finalPath
 */
export function processVideoUrl(
  vidurl: string,
  source: string | null | undefined,
  config?: { m3u8_host_encrypt?: string },
  data?: { source?: { m3u8_host?: string } }
): { finalSource: string; finalPath: string; fullUrl: string } {
  // Step 1: Remove leading slash if present (following original logic)
  let processedVidUrl = vidurl;
  const isVidUrlContainSlash = vidurl[0] === "/";
  if (vidurl && isVidUrlContainSlash) {
    processedVidUrl = vidurl.slice(1, vidurl.length);
  }

  // Step 2: Determine source - prioritize config.m3u8_host_encrypt
  let finalSourceHost: string | null | undefined;
  if (config?.m3u8_host_encrypt) {
    finalSourceHost = config.m3u8_host_encrypt;
  } else if (source) {
    finalSourceHost = source;
  } else if (data?.source?.m3u8_host) {
    finalSourceHost = data.source.m3u8_host;
  }

  // Step 3: Final URL Construction (following original if/else logic)
  let finalSource: string;
  let finalPath: string;

  if (finalSourceHost === "/common/source_tese") {
    finalSource = config?.m3u8_host_encrypt + finalSourceHost + "/";
    finalPath = "/common/source_tese/" + processedVidUrl;
  } else {
    finalSource = finalSourceHost?.endsWith("/")
      ? finalSourceHost
      : finalSourceHost + "/";
    finalPath = processedVidUrl;
  }

  // Step 4: Path Processing & Key Generation (following original logic)
  finalPath = finalPath.startsWith("/") ? finalPath : "/" + finalPath;
  finalPath = finalPath.replace(/\/+/g, "/");

  processedVidUrl = processedVidUrl.replace(/\/+/g, "/");
  const vidKeyParam = addVidKeyParam(finalPath);

  // Step 5: Final URL assembly
  const fullUrl = `${finalSource}${processedVidUrl}${vidKeyParam}`;

  return {
    finalSource,
    finalPath,
    fullUrl,
  };
}

/**
 * Format seconds as mm:ss or h:mm:ss
 * @param seconds - duration in seconds
 * @returns formatted string
 */
export function formatDuration(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) return "0:00";
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = Math.floor(seconds % 60);
  if (h > 0) {
    return `${h}:${m.toString().padStart(2, "0")}:${s
      .toString()
      .padStart(2, "0")}`;
  }
  return `${m}:${s.toString().padStart(2, "0")}`;
}
