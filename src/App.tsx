import React from "react";
import { DynamicComponent } from "./utils/dynamicLoader";
import { getBuildConfig } from "./utils/buildConfig";
import { getPageData } from "./data/pageData";
import { BuildType } from "./types/index";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryProvider } from "./providers/QueryProvider";
import { ConfigProvider } from "./providers/ConfigProvider";

interface AppProps {
  templateType?: BuildType;
}

const App: React.FC<AppProps> = ({ templateType }) => {
  const buildType: BuildType =
    templateType || (process.env.TEMPLATE_TYPE as BuildType) || "1";
  const config = getBuildConfig(buildType);

  return (
    <QueryProvider>
      <ConfigProvider>
        <Router>
          <DynamicComponent
            templateId={buildType}
            componentName="layout"
            props={{ config }}
          >
            <Routes>
              <Route
                path="/"
                element={
                  <DynamicComponent
                    templateId={buildType}
                    componentName="homepage"
                    props={{
                      data: getPageData(),
                      config,
                    }}
                  />
                }
              />
              <Route
                path="/post-detail/:id"
                element={
                  <DynamicComponent
                    templateId={buildType}
                    componentName="postdetail"
                    props={{
                      config,
                    }}
                  />
                }
              />
            </Routes>
          </DynamicComponent>
        </Router>
      </ConfigProvider>
    </QueryProvider>
  );
};

export default App;
