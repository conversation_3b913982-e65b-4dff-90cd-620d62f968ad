import { style, styleVariants, keyframes } from "@vanilla-extract/css";

// Design tokens
export const tokens = {
  colors: {
    primary: "#FB8EB7",
    secondary: "#EAA6C7",
    tertiary: "#D9BFD9",
    background: "#f9fafb",
    surface: "#ffffff",
    text: {
      primary: "#111827",
      secondary: "#6b7280",
      muted: "#9ca3af",
    },
    border: "#e5e7eb",
    error: "#dc2626",
    errorBg: "#fef2f2",
    errorBorder: "#fecaca",
    footer: "#1f2937",
  },
  spacing: {
    1: "0.25rem",
    2: "0.5rem",
    3: "0.75rem",
    4: "1rem",
    6: "1.5rem",
    8: "2rem",
    12: "3rem",
    16: "4rem",
  },
  typography: {
    fontFamily: "Georgia, serif",
    fontSize: {
      sm: "0.875rem",
      base: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
  },
  breakpoints: {
    sm: "screen and (min-width: 640px)",
    md: "screen and (min-width: 768px)",
    lg: "screen and (min-width: 1024px)",
    xl: "screen and (min-width: 1280px)",
  },
  shadows: {
    sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    base: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
    md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
  },
  borderRadius: {
    sm: "0.25rem",
    base: "0.375rem",
    md: "0.5rem",
    lg: "0.75rem",
    xl: "1rem",
  },
};

// Keyframes
export const spin = keyframes({
  from: { transform: "rotate(0deg)" },
  to: { transform: "rotate(360deg)" },
});

export const fadeIn = keyframes({
  from: { opacity: 0, transform: "translateY(20px)" },
  to: { opacity: 1, transform: "translateY(0)" },
});

// Base styles
export const container = style({
  minHeight: "100vh",
  backgroundColor: tokens.colors.background,
  fontFamily: tokens.typography.fontFamily,
  display: "flex",
  flexDirection: "column",
});

export const containerContent = style({
  maxWidth: "1152px",
  margin: "0 auto",
  padding: `0 ${tokens.spacing[4]}`,

  "@media": {
    [tokens.breakpoints.lg]: {
      padding: `0 ${tokens.spacing[8]}`,
    },
  },
});

export const header = style({
  background: `linear-gradient(90deg, ${tokens.colors.primary}, ${tokens.colors.secondary}, ${tokens.colors.tertiary})`,
  boxShadow: tokens.shadows.base,
  borderBottom: `1px solid ${tokens.colors.border}`,
});

export const headerInner = style({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  padding: `${tokens.spacing[6]} 0`,
});

export const logoLink = style({
  display: "block",
  transition: "opacity 0.2s ease",

  ":hover": {
    opacity: 0.8,
  },
});

export const logo = style({
  height: "2.5rem",
  width: "auto",
  cursor: "pointer",
});

export const nav = style({
  display: "none",

  "@media": {
    [tokens.breakpoints.md]: {
      display: "flex",
      gap: tokens.spacing[8],
    },
  },
});

export const navButton = style({
  background: "none",
  border: "none",
  color: "white",
  fontSize: tokens.typography.fontSize.base,
  fontWeight: tokens.typography.fontWeight.medium,
  cursor: "pointer",
  padding: `${tokens.spacing[2]} ${tokens.spacing[4]}`,
  borderRadius: tokens.borderRadius.md,
  transition: "all 0.2s ease",

  ":hover": {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    transform: "translateY(-1px)",
  },
});

export const main = style({
  flex: 1,
  padding: `${tokens.spacing[12]} 0`,
});

export const footer = style({
  backgroundColor: tokens.colors.footer,
  color: "white",
  padding: `${tokens.spacing[12]} 0`,
  marginTop: "auto",
});

export const footerText = style({
  color: "#d1d5db",
  fontSize: tokens.typography.fontSize.sm,
  margin: 0,
  textAlign: "center",
});

// Content styles
export const contentContainer = style({
  animation: `${fadeIn} 0.6s ease-out`,
});

export const loadingContainer = style({
  textAlign: "center",
  padding: `${tokens.spacing[16]} 0`,
});

export const spinner = style({
  display: "inline-block",
  width: "3rem",
  height: "3rem",
  border: `2px solid ${tokens.colors.primary}`,
  borderTop: "2px solid transparent",
  borderRadius: "50%",
  marginBottom: tokens.spacing[4],
  animation: `${spin} 1s linear infinite`,
});

export const loadingText = style({
  color: tokens.colors.text.secondary,
  fontSize: tokens.typography.fontSize.base,
  margin: 0,
});

export const errorContainer = style({
  textAlign: "center",
  padding: `${tokens.spacing[16]} 0`,
});

export const errorBox = style({
  backgroundColor: tokens.colors.errorBg,
  border: `1px solid ${tokens.colors.errorBorder}`,
  borderRadius: tokens.borderRadius.lg,
  padding: tokens.spacing[6],
  maxWidth: "500px",
  margin: "0 auto",
});

export const errorText = style({
  color: tokens.colors.error,
  margin: `${tokens.spacing[2]} 0`,
  fontWeight: tokens.typography.fontWeight.medium,
});

export const errorSubtext = style({
  color: tokens.colors.text.secondary,
  margin: `${tokens.spacing[2]} 0`,
  fontSize: tokens.typography.fontSize.sm,
});

export const sectionsContainer = style({
  display: "flex",
  flexDirection: "column",
  gap: tokens.spacing[16],
});

export const section = style({
  marginBottom: tokens.spacing[16],
});

export const sectionTitle = style({
  fontSize: tokens.typography.fontSize["2xl"],
  fontWeight: tokens.typography.fontWeight.semibold,
  color: tokens.colors.text.primary,
  marginBottom: tokens.spacing[4],
  position: "relative",
});

export const videoGrid = style({
  display: "grid",
  gap: tokens.spacing[6],
  gridTemplateColumns: "repeat(1, 1fr)",

  "@media": {
    [tokens.breakpoints.sm]: {
      gridTemplateColumns: "repeat(2, 1fr)",
    },
    [tokens.breakpoints.md]: {
      gridTemplateColumns: "repeat(3, 1fr)",
    },
    [tokens.breakpoints.lg]: {
      gridTemplateColumns: "repeat(4, 1fr)",
    },
    [tokens.breakpoints.xl]: {
      gridTemplateColumns: "repeat(5, 1fr)",
    },
  },
});

// Card styles
export const card = style({
  backgroundColor: tokens.colors.surface,
  borderRadius: tokens.borderRadius.lg,
  boxShadow: tokens.shadows.base,
  overflow: "hidden",
});

export const cardLarge = style([
  card,
  {
    boxShadow: tokens.shadows.lg,
  },
]);

export const cardPadding = style({
  padding: tokens.spacing[12],
});

// Ads styles
export const adsSection = style({
  marginBottom: tokens.spacing[16],
});

export const featuredAds = style({
  marginBottom: tokens.spacing[8],
});

export const bannerAds = style({
  marginBottom: tokens.spacing[8],
});

export const adsGrid = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  gap: tokens.spacing[4],
});

export const bannerGrid = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  gap: tokens.spacing[4],

  "@media": {
    [tokens.breakpoints.md]: {
      gridTemplateColumns: "repeat(2, 1fr)",
    },
  },
});

export const adLink = style({
  display: "block",
  textDecoration: "none",
  color: "inherit",
});

export const adCard = style({
  position: "relative",
  overflow: "hidden",
  borderRadius: tokens.borderRadius.lg,
  boxShadow: tokens.shadows.md,
  transition: "box-shadow 0.3s ease, transform 0.3s ease",

  ":hover": {
    boxShadow: tokens.shadows.lg,
    transform: "scale(1.02)",
  },
});

export const adImage = style({
  width: "100%",
  height: "4rem", // h-16 - for 980x60 format
  objectFit: "cover",
  transition: "transform 0.3s ease",

  selectors: {
    [`${adCard}:hover &`]: {
      transform: "scale(1.05)",
    },
  },
});

export const bannerImage = style({
  width: "100%",
  height: "16rem", // h-64 - for 960x400 format
  objectFit: "cover",
  transition: "transform 0.3s ease",

  selectors: {
    [`${adCard}:hover &`]: {
      transform: "scale(1.05)",
    },
  },
});

export const adOverlay = style({
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  background: "linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent)",
  padding: tokens.spacing[2],
});

export const adTitle = style({
  color: "white",
  fontWeight: tokens.typography.fontWeight.medium,
  fontSize: tokens.typography.fontSize.sm,
  margin: 0,
});

// Theme variants
export const themeVariants = styleVariants({
  light: {
    backgroundColor: tokens.colors.surface,
    color: tokens.colors.text.primary,
  },
  dark: {
    backgroundColor: "#1a1a1a",
    color: "#ffffff",
  },
  neon: {
    backgroundColor: "#0a0a0a",
    color: "#00ff00",
  },
});
