import React from "react";
import { Link } from "react-router-dom";
import { LayoutProps } from "../../types/index";
import { HEADER_MENU } from "../../constants/menu";
import { SITE_SETTINGS } from "../../constants/site";
import * as styles from "./styles.css";

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <div className={styles.containerContent}>
          <div className={styles.headerInner}>
            <div>
              <Link to="/" className={styles.logoLink}>
                <img
                  src="/logo.png"
                  alt={SITE_SETTINGS.title}
                  className={styles.logo}
                />
              </Link>
            </div>
            <nav className={styles.nav}>
              {HEADER_MENU.map((item, index) => (
                <button key={index} className={styles.navButton}>
                  {item.name}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </header>

      <main className={styles.main}>
        {children}
      </main>

      <footer className={styles.footer}>
        <div className={styles.containerContent}>
          <p className={styles.footerText}>
            © 2024 {SITE_SETTINGS.title}. 高质量视频内容平台.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
