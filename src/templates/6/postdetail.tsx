import React from "react";
import { useParams } from "react-router-dom";
import VideoPlayer from "../../components/VideoPlayer";
import { useVideoDetail } from "../../hooks/useApi";
import * as styles from "./styles.css";

const PostDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { data: videoData, isLoading, error } = useVideoDetail(id || "");

  if (isLoading) {
    return (
      <div className={styles.containerContent}>
        <div className={styles.loadingContainer}>
          <p className={styles.loadingText}>Loading video details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.containerContent}>
        <div className={styles.errorContainer}>
          <p className={styles.errorText}>
            Error loading video: {error.message}
          </p>
        </div>
      </div>
    );
  }

  if (!videoData?.data) {
    return (
      <div className={styles.containerContent}>
        <div className={styles.errorContainer}>
          <p className={styles.errorText}>Video not found</p>
        </div>
      </div>
    );
  }

  const video = videoData.data;

  return (
    <div className={styles.containerContent}>
      <div style={{ marginBottom: "3rem" }}>
        <div className={styles.cardLarge}>
          <VideoPlayer
            videoUrl={video.source?.video_url || video.info?.video_url || ""}
            source={
              video.source?.m3u8_host ||
              video.source?.m3u8_host1 ||
              video.source?.m3u8_host2
            }
            poster={video.source?.thumb || video.info?.thumb}
          />
        </div>
      </div>

      <div className={`${styles.card} ${styles.cardPadding}`}>
        <h1
          style={{
            fontSize: "1.875rem",
            fontWeight: 700,
            color: "#111827",
            marginBottom: "1.5rem",
            lineHeight: 1.2,
          }}
        >
          {video.info?.title || "Untitled Video"}
        </h1>

        <div
          style={{
            display: "flex",
            flexWrap: "wrap",
            gap: "1.5rem",
            marginBottom: "2rem",
            paddingBottom: "2rem",
            borderBottom: "1px solid #e5e7eb",
          }}
        >
          {video.info?.publisher && (
            <div
              style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}
            >
              <span
                style={{
                  fontWeight: 500,
                  color: "#6b7280",
                  fontSize: "0.875rem",
                }}
              >
                Publisher:
              </span>
              <span
                style={{
                  color: "#111827",
                  fontSize: "0.875rem",
                }}
              >
                {video.info.publisher}
              </span>
            </div>
          )}
          {video.info?.duration && (
            <div
              style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}
            >
              <span
                style={{
                  fontWeight: 500,
                  color: "#6b7280",
                  fontSize: "0.875rem",
                }}
              >
                Duration:
              </span>
              <span
                style={{
                  color: "#111827",
                  fontSize: "0.875rem",
                }}
              >
                {video.info.duration}
              </span>
            </div>
          )}
          {video.info?.insert_time && (
            <div
              style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}
            >
              <span
                style={{
                  fontWeight: 500,
                  color: "#6b7280",
                  fontSize: "0.875rem",
                }}
              >
                Published:
              </span>
              <span
                style={{
                  color: "#111827",
                  fontSize: "0.875rem",
                }}
              >
                {video.info.insert_time}
              </span>
            </div>
          )}
        </div>

        {video.info?.description && (
          <div
            style={{
              color: "#111827",
              lineHeight: 1.6,
              fontSize: "1rem",
            }}
          >
            <p>{video.info.description}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PostDetail;
