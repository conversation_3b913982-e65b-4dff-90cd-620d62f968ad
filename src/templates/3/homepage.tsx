import React from "react";
import { HomepageProps } from "../../types/index";
import { useHomeData, useAdsData } from "../../hooks/useApi";
import VideoCard from "../../components/VideoCard";
import Image from "../../components/Image";
import { SECTION_NAMES } from "../../constants/sections";

const Homepage: React.FC<HomepageProps> = () => {
  const { data: apiData, isLoading, error } = useHomeData();
  const { data: adsData } = useAdsData();

  if (isLoading) {
    return (
      <div
        style={{ maxWidth: "1152px", margin: "0 auto", padding: "3rem 1rem" }}
      >
        <div style={{ textAlign: "center" }}>
          <div
            style={{
              display: "inline-block",
              width: "3rem",
              height: "3rem",
              border: "2px solid #10B981",
              borderTop: "2px solid transparent",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
              marginBottom: "1rem",
            }}
          ></div>
          <p style={{ color: "#6b7280" }}>Loading content...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        style={{ maxWidth: "1152px", margin: "0 auto", padding: "3rem 1rem" }}
      >
        <div style={{ textAlign: "center" }}>
          <div
            style={{
              backgroundColor: "#fef2f2",
              border: "1px solid #fecaca",
              borderRadius: "0.5rem",
              padding: "1.5rem",
            }}
          >
            <p style={{ color: "#dc2626", margin: "0.5rem 0" }}>
              Error loading content: {error.message}
            </p>
            <p style={{ color: "#6b7280", margin: "0.5rem 0" }}>
              Showing fallback content...
            </p>
          </div>
        </div>
      </div>
    );
  }

  const containerStyle: React.CSSProperties = {
    maxWidth: "1152px", // max-w-6xl
    margin: "0 auto",
    padding: "3rem 1rem",
  };

  return (
    <>
      <style
        dangerouslySetInnerHTML={{
          __html: `
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          
          .video-grid-responsive {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 1.5rem;
          }
          
          @media (min-width: 640px) {
            .video-grid-responsive {
              grid-template-columns: repeat(2, 1fr);
            }
          }
          
          @media (min-width: 768px) {
            .video-grid-responsive {
              grid-template-columns: repeat(3, 1fr);
            }
          }
          
          @media (min-width: 1024px) {
            .video-grid-responsive {
              grid-template-columns: repeat(4, 1fr);
            }
          }

          @media (min-width: 1280px) {
            .video-grid-responsive {
              grid-template-columns: repeat(5, 1fr);
            }
          }

          @media (min-width: 1536px) {
            .video-grid-responsive {
              grid-template-columns: repeat(5, 1fr);
            }
          }

          .ads-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          .banner-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          @media (min-width: 768px) {
            .banner-grid {
              grid-template-columns: repeat(2, 1fr);
            }
          }

          .ad-card {
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.3s ease, transform 0.3s ease;
          }

          .ad-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: scale(1.02);
          }

          .ad-image {
            width: 100%;
            height: 4rem;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          .ad-card:hover .ad-image {
            transform: scale(1.05);
          }

          .banner-image {
            width: 100%;
            height: 16rem;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          .ad-card:hover .banner-image {
            transform: scale(1.05);
          }

          .ad-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
            padding: 0.5rem;
          }

          .ad-title {
            color: white;
            font-weight: 500;
            font-size: 0.75rem;
            margin: 0;
          }
        `,
        }}
      />
      <div style={containerStyle} className="container-responsive">
        {/* Ads Section */}
        {adsData?.data && (
          <div style={{ marginBottom: "4rem" }}>
            {/* Horizontal Ads */}
            {adsData.data.ads?.shouyehengfu &&
              adsData.data.ads.shouyehengfu.length > 0 && (
                <div style={{ marginBottom: "2rem" }}>
                  <div className="ads-grid">
                    {adsData.data.ads.shouyehengfu
                      .filter((ad) => ad.status === 1)
                      .map((ad, index) => (
                        <a
                          key={index}
                          href={ad.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <div className="ad-card">
                            <Image
                              src={ad.local_img || ad.img}
                              alt={ad.name}
                              className="ad-image"
                              width="100%"
                              height="4rem"
                            />
                            <div className="ad-overlay">
                              <h4 className="ad-title">{ad.name}</h4>
                            </div>
                          </div>
                        </a>
                      ))}
                  </div>
                </div>
              )}

            {/* Banner Ads */}
            {adsData.data.banners && adsData.data.banners.length > 0 && (
              <div style={{ marginBottom: "2rem" }}>
                <div className="banner-grid">
                  {adsData.data.banners.map((banner, index) => (
                    <a
                      key={index}
                      href={banner.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ textDecoration: "none", color: "inherit" }}
                    >
                      <div className="ad-card">
                        <Image
                          src={banner.local_img || banner.img}
                          alt={banner.title}
                          className="banner-image"
                          width="100%"
                          height="16rem"
                        />
                        <div className="ad-overlay">
                          <h4 className="ad-title">{banner.title}</h4>
                        </div>
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* API Content Section */}
        {apiData?.data && (
          <div
            style={{ display: "flex", flexDirection: "column", gap: "4rem" }}
          >
            {Object.entries(apiData.data)
              .filter(([key]) => key !== "seo" && key !== "notice")
              .map(([, value], i) => {
                if (!value || !value.data || value.data.length === 0) {
                  return null;
                }

                const title =
                  value.channel !== "topic"
                    ? SECTION_NAMES[i]
                    : value.data[0]?.title;
                const listData =
                  value.channel === "topic" ? value.data[0]?.list : value.data;

                return (
                  <div key={i} style={{ marginBottom: "4rem" }}>
                    <h3
                      style={{
                        fontSize: "1.5rem",
                        fontWeight: "600",
                        color: "#111827",
                        marginBottom: "1rem",
                      }}
                    >
                      {title}
                    </h3>
                    <div
                      style={{
                        display: "grid",
                        gap: "1.5rem",
                      }}
                      className="video-grid-responsive"
                    >
                      {listData.map((item: any) => (
                        <VideoCard
                          key={item.id}
                          id={item.id}
                          title={item.title}
                          performer={item.publisher || ""}
                          thumb={item.thumb}
                          duration={item.duration}
                          insert_time={item.insert_time}
                          isVip={item.channel === "vip"}
                        />
                      ))}
                    </div>
                  </div>
                );
              })}
          </div>
        )}
      </div>
    </>
  );
};

export default Homepage;
