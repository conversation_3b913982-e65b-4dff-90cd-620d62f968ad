import React from "react";
import { useParams } from "react-router-dom";
import VideoPlayer from "../../components/VideoPlayer";
import { useVideoDetail } from "../../hooks/useApi";

interface Ad {
  id: number;
  site: number;
  name: string;
  img: string;
  local_img: string;
  url: string;
  position: number;
  sort: number;
  insert_time: number;
  update_time: number;
  status: number;
  is_show: number;
  rmk: null;
  remote: number;
}

interface Info {
  id: number;
  title: string;
  tags: string;
  channel: string;
  insert_time: number;
  update_time: number;
  thumb: string;
  video_url: string;
  down_url: string;
  actors: null;
  keywords: null;
  description: string;
  sync_bbs: number;
  thumb_series: string;
  thumb_ori: null;
  jump_name: string;
  thumb_series_replace: string[];
}

interface Source {
  gao_img: string;
  gao_url: string;
  isImgGao: boolean;
  isVideoGao: boolean;
  m3u8_host: string;
  m3u8_host1: string;
  m3u8_host2: string;
  video_url: string;
  thumb: string;
  channel: string;
  down_url: string;
  playId: string;
  nav_id: number;
}

interface VideoDetail {
  ad: Ad;
  info: Info;
  playId: string;
  source: Source;
}

const PostDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const { data: videoDetail, isLoading, error } = useVideoDetail(id || "");

  const containerStyle: React.CSSProperties = {
    minHeight: "100vh",
    backgroundColor: "#f9fafb",
    padding: "2rem 0",
  };

  const contentStyle: React.CSSProperties = {
    maxWidth: "1152px",
    margin: "0 auto",
    padding: "0 1rem",
  };

  const loadingStyle: React.CSSProperties = {
    animation: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
  };

  const videoSectionStyle: React.CSSProperties = {
    backgroundColor: "#ffffff",
    borderRadius: "0.5rem",
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
    overflow: "hidden",
    marginBottom: "2rem",
  };

  const videoPlayerStyle: React.CSSProperties = {
    aspectRatio: "16 / 9",
  };

  const infoSectionStyle: React.CSSProperties = {
    backgroundColor: "#ffffff",
    borderRadius: "0.5rem",
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
    padding: "1.5rem",
    marginBottom: "2rem",
  };

  const titleStyle: React.CSSProperties = {
    fontSize: "1.875rem",
    fontWeight: "bold",
    color: "#111827",
    marginBottom: "1rem",
  };

  const metadataStyle: React.CSSProperties = {
    display: "flex",
    flexWrap: "wrap",
    gap: "1rem",
    fontSize: "0.875rem",
    color: "#6b7280",
    marginBottom: "1.5rem",
  };

  const metaItemStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
  };

  const sectionTitleStyle: React.CSSProperties = {
    fontSize: "1.125rem",
    fontWeight: "600",
    color: "#111827",
    marginBottom: "0.75rem",
  };

  const descriptionTextStyle: React.CSSProperties = {
    color: "#374151",
    lineHeight: "1.625",
  };

  const tagsStyle: React.CSSProperties = {
    display: "flex",
    flexWrap: "wrap",
    gap: "0.5rem",
  };

  const tagStyle: React.CSSProperties = {
    display: "inline-block",
    backgroundColor: "#dbeafe",
    color: "#1e40af",
    fontSize: "0.875rem",
    padding: "0.25rem 0.75rem",
    borderRadius: "9999px",
  };

  const errorCardStyle: React.CSSProperties = {
    backgroundColor: "#ffffff",
    borderRadius: "0.5rem",
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
    padding: "2rem",
    textAlign: "center",
  };

  if (isLoading) {
    return (
      <div style={containerStyle}>
        <div style={contentStyle}>
          <div style={loadingStyle}>
            <div
              style={{
                backgroundColor: "#d1d5db",
                height: "16rem",
                borderRadius: "0.5rem",
                marginBottom: "1.5rem",
              }}
            ></div>
            <div
              style={{
                backgroundColor: "#d1d5db",
                height: "2rem",
                borderRadius: "0.25rem",
                marginBottom: "1rem",
              }}
            ></div>
            <div
              style={{
                backgroundColor: "#d1d5db",
                height: "1rem",
                borderRadius: "0.25rem",
                marginBottom: "0.5rem",
              }}
            ></div>
            <div
              style={{
                backgroundColor: "#d1d5db",
                height: "1rem",
                borderRadius: "0.25rem",
                width: "75%",
              }}
            ></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !videoDetail) {
    return (
      <div style={containerStyle}>
        <div style={contentStyle}>
          <div style={errorCardStyle}>
            <div style={{ fontSize: "4rem", marginBottom: "1rem" }}>⚠️</div>
            <h2
              style={{
                fontSize: "1.5rem",
                fontWeight: "bold",
                color: "#111827",
                marginBottom: "1rem",
              }}
            >
              视频未找到
            </h2>
            <p style={{ color: "#6b7280" }}>
              抱歉，您请求的视频不存在或已被删除。
            </p>
          </div>
        </div>
      </div>
    );
  }

  const video = videoDetail?.data as VideoDetail;

  return (
    <div style={containerStyle}>
      <div style={contentStyle}>
        {/* Video Player Section */}
        <div style={videoSectionStyle}>
          <div style={videoPlayerStyle}>
            <VideoPlayer
              videoUrl={video.source?.video_url || video.info?.video_url || ""}
              source={
                video.source?.m3u8_host ||
                video.source?.m3u8_host1 ||
                video.source?.m3u8_host2
              }
              poster={video.source?.thumb || video.info?.thumb}
              className="w-full h-full"
              onReady={(player) => {
                console.log("Video player ready:", player);
              }}
              onError={(error) => {
                console.error("Video player error:", error);
              }}
            />
          </div>
        </div>

        {/* Video Info Section */}
        <div style={infoSectionStyle}>
          <h1 style={titleStyle}>{video.info?.title}</h1>

          <div style={metadataStyle}>
            {video.info?.insert_time && (
              <span style={metaItemStyle}>
                <span style={{ marginRight: "0.25rem" }}>🕒</span>
                {new Date(video.info.insert_time * 1000).toLocaleDateString()}
              </span>
            )}

            {video.info?.channel && (
              <span style={metaItemStyle}>
                <span style={{ marginRight: "0.25rem" }}>📺</span>
                {video.info.channel}
              </span>
            )}
          </div>

          {video.info?.description && (
            <div style={{ marginBottom: "1.5rem" }}>
              <h3 style={sectionTitleStyle}>视频描述</h3>
              <p style={descriptionTextStyle}>{video.info.description}</p>
            </div>
          )}

          {video.info?.tags && (
            <div style={{ marginTop: "1.5rem" }}>
              <h3 style={sectionTitleStyle}>标签</h3>
              <div style={tagsStyle}>
                {video.info.tags.split(",").map((tag, index) => (
                  <span key={index} style={tagStyle}>
                    {tag.trim()}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Related Videos Section */}
        <div style={infoSectionStyle}>
          <h3 style={sectionTitleStyle}>相关视频</h3>
          <div style={{ color: "#6b7280" }}>
            <p>相关视频功能即将推出...</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostDetail;
