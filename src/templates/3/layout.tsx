import React from "react";
import { Link } from "react-router-dom";
import { LayoutProps } from "../../types/index";
import { HEADER_MENU } from "../../constants/menu";
import { SITE_SETTINGS } from "../../constants/site";

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const bodyStyle: React.CSSProperties = {
    minHeight: "100vh",
    backgroundColor: "#f9fafb",
    fontFamily: "serif",
  };

  const containerStyle: React.CSSProperties = {
    minHeight: "100vh",
    display: "flex",
    flexDirection: "column",
  };

  const headerStyle: React.CSSProperties = {
    background: "linear-gradient(90deg, #FB8EB7, #EAA6C7, #D9BFD9)",
    boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
    borderBottom: "1px solid #e5e7eb",
  };

  const headerContentStyle: React.CSSProperties = {
    maxWidth: "1152px", // max-w-6xl
    margin: "0 auto",
    padding: "1.5rem 1rem",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  };

  const navStyle: React.CSSProperties = {
    display: "none",
    gap: "2rem",
  };

  const navLinkStyle: React.CSSProperties = {
    color: "#ffffff",
    textDecoration: "none",
    transition: "color 0.3s ease",
    background: "transparent",
    border: "none",
    cursor: "pointer",
    fontFamily: "inherit",
    fontSize: "inherit",
  };

  const mainStyle: React.CSSProperties = {
    flex: 1,
  };

  const footerStyle: React.CSSProperties = {
    backgroundColor: "#1f2937",
    color: "#ffffff",
    padding: "3rem 0",
  };

  const footerContentStyle: React.CSSProperties = {
    maxWidth: "1152px", // max-w-6xl
    margin: "0 auto",
    padding: "0 1rem",
    textAlign: "center",
  };

  const footerTextStyle: React.CSSProperties = {
    color: "#d1d5db",
    margin: 0,
  };

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>{SITE_SETTINGS.title}</title>
        <meta name="description" content={SITE_SETTINGS.description} />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <style
          dangerouslySetInnerHTML={{
            __html: `
            @media (min-width: 768px) {
              .nav-responsive {
                display: flex !important;
              }
              .header-content-responsive {
                padding: 1.5rem 2rem !important;
              }
              .footer-content-responsive {
                padding: 0 2rem !important;
              }
              .features-responsive {
                grid-template-columns: repeat(3, 1fr) !important;
              }
              .button-group-responsive {
                flex-direction: row !important;
              }
              .container-responsive {
                padding: 3rem 2rem !important;
              }
            }

            @media (min-width: 640px) {
              .button-group-responsive {
                flex-direction: row !important;
              }
            }

            @media (min-width: 1024px) {
              .title-responsive {
                font-size: 3.75rem !important;
              }
            }
          `,
          }}
        />
      </head>
      <body style={bodyStyle}>
        <div style={containerStyle}>
          <header style={headerStyle}>
            <div
              style={headerContentStyle}
              className="header-content-responsive"
            >
              <div>
                <Link
                  to="/"
                  style={{
                    display: "block",
                    transition: "opacity 0.2s ease",
                  }}
                  onMouseEnter={(e) => (e.currentTarget.style.opacity = "0.8")}
                  onMouseLeave={(e) => (e.currentTarget.style.opacity = "1")}
                >
                  <img
                    src="/logo.png"
                    alt={SITE_SETTINGS.title}
                    style={{
                      height: "2.5rem",
                      width: "auto",
                      margin: 0,
                      cursor: "pointer",
                    }}
                  />
                </Link>
              </div>
              <nav style={navStyle} className="nav-responsive">
                {HEADER_MENU.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      // Handle navigation based on channel or code
                    }}
                    style={navLinkStyle}
                  >
                    {item.name}
                  </button>
                ))}
              </nav>
            </div>
          </header>

          <main style={mainStyle}>{children}</main>

          <footer style={footerStyle}>
            <div
              style={footerContentStyle}
              className="footer-content-responsive"
            >
              <p style={footerTextStyle}>
                © 2024 {SITE_SETTINGS.title}. 高质量视频内容平台.
              </p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
};

export default Layout;
