.body {
  min-height: 100vh;
  background-color: #f9fafb;
  font-family: serif;
}

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(90deg, #FB8EB7, #EAA6C7, #D9BFD9);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e5e7eb;
}

.headerContent {
  max-width: 1152px; /* max-w-6xl */
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.logoLink {
  display: block;
  transition: opacity 0.2s ease;
}

.logoLink:hover {
  opacity: 0.8;
}

.logo {
  height: 2.5rem;
  width: auto;
  margin: 0;
  cursor: pointer;
}

.nav {
  display: none;
  gap: 2rem;
}

.navLink {
  color: #ffffff;
  text-decoration: none;
  transition: color 0.3s ease;
  background: transparent;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
}

.navLink:hover {
  color: #e5e7eb;
}

.main {
  flex: 1;
}

.footer {
  background-color: #1f2937;
  color: #ffffff;
  padding: 3rem 0;
}

.footerContent {
  max-width: 1152px; /* max-w-6xl */
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
}

.footerText {
  color: #d1d5db;
  margin: 0;
}

@media (min-width: 768px) {
  .nav {
    display: flex;
  }

  .headerContent {
    padding: 1.5rem 2rem;
  }

  .footerContent {
    padding: 0 2rem;
  }
}