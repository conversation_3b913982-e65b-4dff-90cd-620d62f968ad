import React from "react";
import { HomepageProps } from "../../types/index";
import { useHomeData, useAdsData } from "../../hooks/useApi";
import VideoCard from "../../components/VideoCard";
import Image from "../../components/Image";
import { SECTION_NAMES } from "../../constants/sections";
import styles from "./homepage.module.css";

const Homepage: React.FC<HomepageProps> = () => {
  const { data: apiData, isLoading, error } = useHomeData();
  const { data: adsData } = useAdsData();
  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading content...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <p>Error loading content: {error.message}</p>
          <p>Showing fallback content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Ads Section */}
      {adsData?.data && (
        <div className={styles.adsSection}>
          {/* Horizontal Ads */}
          {adsData.data.ads?.shouyehengfu &&
            adsData.data.ads.shouyehengfu.length > 0 && (
              <div className={styles.featuredAds}>
                <div className={styles.adsGrid}>
                  {adsData.data.ads.shouyehengfu
                    .filter((ad) => ad.status === 1)
                    .map((ad, index) => (
                      <a
                        key={index}
                        href={ad.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={styles.adLink}
                      >
                        <div className={styles.adCard}>
                          <Image
                            src={ad.local_img || ad.img}
                            alt={ad.name}
                            className={styles.adImage}
                            width="100%"
                            height="4rem"
                          />
                          <div className={styles.adOverlay}>
                            <h4 className={styles.adTitle}>{ad.name}</h4>
                          </div>
                        </div>
                      </a>
                    ))}
                </div>
              </div>
            )}

          {/* Banner Ads */}
          {adsData.data.banners && adsData.data.banners.length > 0 && (
            <div className={styles.bannerAds}>
              <div className={styles.bannerGrid}>
                {adsData.data.banners.map((banner, index) => (
                  <a
                    key={index}
                    href={banner.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.adLink}
                  >
                    <div className={styles.adCard}>
                      <Image
                        src={banner.local_img || banner.img}
                        alt={banner.title}
                        className={styles.bannerImage}
                        width="100%"
                        height="16rem"
                      />
                      <div className={styles.adOverlay}>
                        <h4 className={styles.adTitle}>{banner.title}</h4>
                      </div>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* API Content Section */}
      {apiData?.data && (
        <div className={styles.apiContent}>
          {Object.entries(apiData.data)
            .filter(([key]) => key !== "seo" && key !== "notice")
            .map(([, value], i) => {
              if (!value || !value.data || value.data.length === 0) {
                return null;
              }

              const title =
                value.channel !== "topic"
                  ? SECTION_NAMES[i]
                  : value.data[0]?.title;
              const listData =
                value.channel === "topic" ? value.data[0]?.list : value.data;

              return (
                <div key={i} className={styles.videoSection}>
                  <h3 className={styles.sectionTitle}>{title}</h3>
                  <div className={styles.videoGrid}>
                    {listData.map((item: any) => (
                      <VideoCard
                        key={item.id}
                        id={item.id}
                        title={item.title}
                        performer={item.publisher || ""}
                        thumb={item.thumb}
                        duration={item.duration}
                        insert_time={item.insert_time}
                        isVip={item.channel === "vip"}
                      />
                    ))}
                  </div>
                </div>
              );
            })}
        </div>
      )}
    </div>
  );
};

export default Homepage;
