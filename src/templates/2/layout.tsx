import React from "react";
import { <PERSON> } from "react-router-dom";
import { LayoutProps } from "../../types/index";
import { HEADER_MENU } from "../../constants/menu";
import { SITE_SETTINGS } from "../../constants/site";
import styles from "./layout.module.css";

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <div>
            <Link to="/" className={styles.logoLink}>
              <img
                src="/logo.png"
                alt={SITE_SETTINGS.title}
                className={styles.logo}
              />
            </Link>
          </div>
          <nav className={styles.nav}>
            {HEADER_MENU.map((item, index) => (
              <button
                key={index}
                onClick={() => {
                  // Handle navigation based on channel or code
                }}
                className={styles.navLink}
              >
                {item.name}
              </button>
            ))}
          </nav>
        </div>
      </header>

      <main className={styles.main}>{children}</main>

      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <p className={styles.footerText}>
            © 2024 {SITE_SETTINGS.title}. 高质量视频内容平台.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
