.container {
  max-width: 1152px; /* max-w-6xl */
  margin: 0 auto;
  padding: 3rem 1rem;
}



.loading {
  text-align: center;
  padding: 4rem 0;
}

.spinner {
  display: inline-block;
  width: 3rem;
  height: 3rem;
  border: 2px solid #10B981;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading p {
  margin: 0;
  color: #6b7280;
}

.error {
  text-align: center;
  padding: 4rem 0;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  margin: 2rem 0;
}

.error p {
  margin: 0.5rem 0;
  color: #dc2626;
}

/* API Content Styles */
.apiContent {
  margin-bottom: 4rem;
}

/* Video Section Styles */
.videoSection {
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.videoGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
}

/* Responsive grid for different screen sizes */
@media (min-width: 640px) {
  .videoGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .videoGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .videoGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .videoGrid {
    grid-template-columns: repeat(5, 1fr);
  }
}



@media (min-width: 768px) {
  .container {
    padding: 3rem 2rem;
  }
}

/* Ads Section Styles */
.adsSection {
  margin-bottom: 4rem;
}

.featuredAds {
  margin-bottom: 2rem;
}

.bannerAds {
  margin-bottom: 2rem;
}

.adsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.bannerGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .bannerGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.adLink {
  display: block;
  text-decoration: none;
  color: inherit;
}

.adCard {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.adCard:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.02);
}

.adImage {
  width: 100%;
  height: 4rem; /* h-16 - for 980x60 format */
  object-fit: cover;
  transition: transform 0.3s ease;
}

.adCard:hover .adImage {
  transform: scale(1.05);
}

.bannerImage {
  width: 100%;
  height: 16rem; /* h-64 - for 960x400 format */
  object-fit: cover;
  transition: transform 0.3s ease;
}

.adCard:hover .bannerImage {
  transform: scale(1.05);
}

.adOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 0.5rem;
}

.adTitle {
  color: white;
  font-weight: 500;
  font-size: 0.75rem; /* text-xs */
  margin: 0;
}