.container {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 2rem 0;
}

.content {
  max-width: 1152px; /* max-w-6xl */
  margin: 0 auto;
  padding: 0 1rem;
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loadingVideo {
  background-color: #d1d5db;
  height: 16rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.loadingTitle {
  background-color: #d1d5db;
  height: 2rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

.loadingText {
  background-color: #d1d5db;
  height: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

.loadingTextShort {
  background-color: #d1d5db;
  height: 1rem;
  border-radius: 0.25rem;
  width: 75%;
}

.errorCard {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
}

.errorIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.errorTitle {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1rem;
}

.errorText {
  color: #6b7280;
}

.videoSection {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.videoPlayer {
  aspect-ratio: 16 / 9;
}

.player {
  width: 100%;
  height: 100%;
}

.infoSection {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.875rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1rem;
}

.metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.metaItem {
  display: flex;
  align-items: center;
}

.metaIcon {
  margin-right: 0.25rem;
}

.description {
  margin-bottom: 1.5rem;
}

.sectionTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
}

.descriptionText {
  color: #374151;
  line-height: 1.625;
}

.tagsSection {
  margin-top: 1.5rem;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  display: inline-block;
  background-color: #dbeafe;
  color: #1e40af;
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
}

.relatedSection {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.relatedPlaceholder {
  color: #6b7280;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@media (min-width: 768px) {
  .content {
    padding: 0 2rem;
  }
}
