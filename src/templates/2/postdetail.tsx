import React from "react";
import { useParams } from "react-router-dom";
import VideoPlayer from "../../components/VideoPlayer";
import { useVideoDetail } from "../../hooks/useApi";
import styles from "./postdetail.module.css";

interface Ad {
  id: number;
  site: number;
  name: string;
  img: string;
  local_img: string;
  url: string;
  position: number;
  sort: number;
  insert_time: number;
  update_time: number;
  status: number;
  is_show: number;
  rmk: null;
  remote: number;
}

interface Info {
  id: number;
  title: string;
  tags: string;
  channel: string;
  insert_time: number;
  update_time: number;
  thumb: string;
  video_url: string;
  down_url: string;
  actors: null;
  keywords: null;
  description: string;
  sync_bbs: number;
  thumb_series: string;
  thumb_ori: null;
  jump_name: string;
  thumb_series_replace: string[];
}

interface Source {
  gao_img: string;
  gao_url: string;
  isImgGao: boolean;
  isVideoGao: boolean;
  m3u8_host: string;
  m3u8_host1: string;
  m3u8_host2: string;
  video_url: string;
  thumb: string;
  channel: string;
  down_url: string;
  playId: string;
  nav_id: number;
}

interface VideoDetail {
  ad: Ad;
  info: Info;
  playId: string;
  source: Source;
}

const PostDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const { data: videoDetail, isLoading, error } = useVideoDetail(id || "");

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.loading}>
            <div className={styles.loadingVideo}></div>
            <div className={styles.loadingTitle}></div>
            <div className={styles.loadingText}></div>
            <div className={styles.loadingTextShort}></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !videoDetail) {
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.errorCard}>
            <div className={styles.errorIcon}>⚠️</div>
            <h2 className={styles.errorTitle}>视频未找到</h2>
            <p className={styles.errorText}>
              抱歉，您请求的视频不存在或已被删除。
            </p>
          </div>
        </div>
      </div>
    );
  }

  const video = videoDetail?.data as VideoDetail;

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* Video Player Section */}
        <div className={styles.videoSection}>
          <div className={styles.videoPlayer}>
            <VideoPlayer
              videoUrl={video.source?.video_url || video.info?.video_url || ""}
              source={
                video.source?.m3u8_host ||
                video.source?.m3u8_host1 ||
                video.source?.m3u8_host2
              }
              poster={video.source?.thumb || video.info?.thumb}
              className={styles.player}
              onReady={(player) => {
                console.log("Video player ready:", player);
              }}
              onError={(error) => {
                console.error("Video player error:", error);
              }}
            />
          </div>
        </div>

        {/* Video Info Section */}
        <div className={styles.infoSection}>
          <h1 className={styles.title}>{video.info?.title}</h1>

          <div className={styles.metadata}>
            {video.info?.insert_time && (
              <span className={styles.metaItem}>
                <span className={styles.metaIcon}>🕒</span>
                {new Date(video.info.insert_time * 1000).toLocaleDateString()}
              </span>
            )}

            {video.info?.channel && (
              <span className={styles.metaItem}>
                <span className={styles.metaIcon}>📺</span>
                {video.info.channel}
              </span>
            )}
          </div>

          {video.info?.description && (
            <div className={styles.description}>
              <h3 className={styles.sectionTitle}>视频描述</h3>
              <p className={styles.descriptionText}>{video.info.description}</p>
            </div>
          )}

          {video.info?.tags && (
            <div className={styles.tagsSection}>
              <h3 className={styles.sectionTitle}>标签</h3>
              <div className={styles.tags}>
                {video.info.tags
                  .split(",")
                  .map((tag: string, index: number) => (
                    <span key={index} className={styles.tag}>
                      {tag.trim()}
                    </span>
                  ))}
              </div>
            </div>
          )}
        </div>

        {/* Related Videos Section */}
        <div className={styles.relatedSection}>
          <h3 className={styles.sectionTitle}>相关视频</h3>
          <div className={styles.relatedPlaceholder}>
            <p>相关视频功能即将推出...</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostDetail;
