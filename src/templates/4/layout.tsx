import React from "react";
import { Link } from "react-router-dom";
import styled, { ThemeProvider } from "styled-components";
import { LayoutProps } from "../../types/index";
import { HEADER_MENU } from "../../constants/menu";
import { SITE_SETTINGS } from "../../constants/site";
import { theme } from "./theme";

// Styled Components with Theme Integration
const Container = styled.div`
  min-height: 100vh;
  background-color: ${(props) => props.theme.colors.background};
  font-family: ${(props) => props.theme.typography.fontFamily};
  display: flex;
  flex-direction: column;
`;

const Header = styled.header`
  background: linear-gradient(
    90deg,
    ${(props) => props.theme.colors.primary},
    ${(props) => props.theme.colors.secondary},
    ${(props) => props.theme.colors.tertiary}
  );
  box-shadow: ${(props) => props.theme.shadows.base};
  border-bottom: 1px solid ${(props) => props.theme.colors.border};
`;

const HeaderContent = styled.div`
  max-width: 1152px;
  margin: 0 auto;
  padding: 0 ${(props) => props.theme.spacing.sm};

  @media (min-width: ${(props) => props.theme.breakpoints.lg}) {
    padding: 0 ${(props) => props.theme.spacing.lg};
  }
`;

const HeaderInner = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${(props) => props.theme.spacing.md} 0;
`;

const LogoContainer = styled.div`
  display: block;
`;

const LogoLink = styled(Link)`
  display: block;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }
`;

const Logo = styled.img`
  height: 2.5rem;
  width: auto;
  cursor: pointer;
`;

const Nav = styled.nav`
  display: none;

  @media (min-width: ${(props) => props.theme.breakpoints.md}) {
    display: flex;
    gap: ${(props) => props.theme.spacing.lg};
  }
`;

const NavButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: ${(props) => props.theme.typography.fontSize.base};
  font-weight: ${(props) => props.theme.typography.fontWeight.medium};
  cursor: pointer;
  padding: ${(props) => props.theme.spacing.xs}
    ${(props) => props.theme.spacing.sm};
  border-radius: ${(props) => props.theme.borderRadius.md};
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }
`;

const Main = styled.main`
  flex: 1;
  padding: ${(props) => props.theme.spacing.xl} 0;
`;

const Footer = styled.footer`
  background-color: ${(props) => props.theme.colors.footer};
  color: white;
  padding: ${(props) => props.theme.spacing.xl} 0;
  margin-top: auto;
`;

const FooterContent = styled.div`
  max-width: 1152px;
  margin: 0 auto;
  padding: 0 ${(props) => props.theme.spacing.sm};
  text-align: center;

  @media (min-width: ${(props) => props.theme.breakpoints.lg}) {
    padding: 0 ${(props) => props.theme.spacing.lg};
  }
`;

const FooterText = styled.p`
  color: #d1d5db;
  font-size: ${(props) => props.theme.typography.fontSize.sm};
  margin: 0;
`;

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <ThemeProvider theme={theme}>
      <Container>
        <Header>
          <HeaderContent>
            <HeaderInner>
              <LogoContainer>
                <LogoLink to="/">
                  <Logo src="/logo.png" alt={SITE_SETTINGS.title} />
                </LogoLink>
              </LogoContainer>
              <Nav>
                {HEADER_MENU.map((item, index) => (
                  <NavButton key={index}>{item.name}</NavButton>
                ))}
              </Nav>
            </HeaderInner>
          </HeaderContent>
        </Header>
        <Main>{children}</Main>
        <Footer>
          <FooterContent>
            <FooterText>
              © 2024 {SITE_SETTINGS.title}. 高质量视频内容平台.
            </FooterText>
          </FooterContent>
        </Footer>
      </Container>
    </ThemeProvider>
  );
};

export default Layout;
