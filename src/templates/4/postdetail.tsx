import React from "react";
import { useParams } from "react-router-dom";
import styled, { ThemeProvider } from "styled-components";
import VideoPlayer from "../../components/VideoPlayer";
import { useVideoDetail } from "../../hooks/useApi";
import { theme } from "./theme";

const Container = styled.div`
  max-width: 1152px;
  margin: 0 auto;
  padding: ${(props) => props.theme.spacing.xl}
    ${(props) => props.theme.spacing.sm};
`;

const VideoSection = styled.div`
  margin-bottom: ${(props) => props.theme.spacing.xl};
`;

const VideoContainer = styled.div`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.lg};
  overflow: hidden;
  box-shadow: ${(props) => props.theme.shadows.lg};
`;

const ContentSection = styled.div`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.lg};
  padding: ${(props) => props.theme.spacing.xl};
  box-shadow: ${(props) => props.theme.shadows.base};
`;

const Title = styled.h1`
  font-size: ${(props) => props.theme.typography.fontSize["3xl"]};
  font-weight: ${(props) => props.theme.typography.fontWeight.bold};
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: ${(props) => props.theme.spacing.md};
  line-height: 1.2;
`;

const MetaInfo = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${(props) => props.theme.spacing.md};
  margin-bottom: ${(props) => props.theme.spacing.lg};
  padding-bottom: ${(props) => props.theme.spacing.lg};
  border-bottom: 1px solid ${(props) => props.theme.colors.border};
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${(props) => props.theme.spacing.xs};
`;

const MetaLabel = styled.span`
  font-weight: ${(props) => props.theme.typography.fontWeight.medium};
  color: ${(props) => props.theme.colors.text.secondary};
  font-size: ${(props) => props.theme.typography.fontSize.sm};
`;

const MetaValue = styled.span`
  color: ${(props) => props.theme.colors.text.primary};
  font-size: ${(props) => props.theme.typography.fontSize.sm};
`;

const Description = styled.div`
  color: ${(props) => props.theme.colors.text.primary};
  line-height: 1.6;
  font-size: ${(props) => props.theme.typography.fontSize.base};
`;

const LoadingContainer = styled.div`
  text-align: center;
  padding: ${(props) => props.theme.spacing.xxl} 0;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const ErrorContainer = styled.div`
  text-align: center;
  padding: ${(props) => props.theme.spacing.xxl} 0;
  color: #dc2626;
`;

const PostDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { data: videoData, isLoading, error } = useVideoDetail(id || "");

  if (isLoading) {
    return (
      <Container>
        <LoadingContainer>
          <p>Loading video details...</p>
        </LoadingContainer>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <ErrorContainer>
          <p>Error loading video: {error.message}</p>
        </ErrorContainer>
      </Container>
    );
  }

  if (!videoData?.data) {
    return (
      <Container>
        <ErrorContainer>
          <p>Video not found</p>
        </ErrorContainer>
      </Container>
    );
  }

  const video = videoData.data;

  return (
    <ThemeProvider theme={theme}>
      <Container>
        <VideoSection>
          <VideoContainer>
            <VideoPlayer
              videoUrl={video.source?.video_url || video.info?.video_url || ""}
              source={
                video.source?.m3u8_host ||
                video.source?.m3u8_host1 ||
                video.source?.m3u8_host2
              }
              poster={video.source?.thumb || video.info?.thumb}
            />
          </VideoContainer>
        </VideoSection>

        <ContentSection>
          <Title>{video.info?.title || "Untitled Video"}</Title>

          <MetaInfo>
            {video.info?.publisher && (
              <MetaItem>
                <MetaLabel>Publisher:</MetaLabel>
                <MetaValue>{video.info.publisher}</MetaValue>
              </MetaItem>
            )}
            {video.info?.duration && (
              <MetaItem>
                <MetaLabel>Duration:</MetaLabel>
                <MetaValue>{video.info.duration}</MetaValue>
              </MetaItem>
            )}
            {video.info?.insert_time && (
              <MetaItem>
                <MetaLabel>Published:</MetaLabel>
                <MetaValue>{video.info.insert_time}</MetaValue>
              </MetaItem>
            )}
          </MetaInfo>

          {video.info?.description && (
            <Description>
              <p>{video.info.description}</p>
            </Description>
          )}
        </ContentSection>
      </Container>
    </ThemeProvider>
  );
};

export default PostDetail;
