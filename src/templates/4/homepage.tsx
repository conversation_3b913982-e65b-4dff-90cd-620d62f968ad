import React from "react";
import styled, { keyframes } from "styled-components";
import { HomepageProps } from "../../types/index";
import { useHomeData, useAdsData } from "../../hooks/useApi";
import VideoCard from "../../components/VideoCard";
import Image from "../../components/Image";
import { SECTION_NAMES } from "../../constants/sections";

// Keyframe animations
const spin = keyframes`
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
`;

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
`;

// Styled Components
const Container = styled.div`
  max-width: 1152px;
  margin: 0 auto;
  padding: ${(props) => props.theme.spacing.xl}
    ${(props) => props.theme.spacing.sm};
  animation: ${fadeIn} 0.6s ease-out;
`;

const LoadingContainer = styled.div`
  text-align: center;
  padding: ${(props) => props.theme.spacing.xxl} 0;
`;

const Spinner = styled.div`
  display: inline-block;
  width: 3rem;
  height: 3rem;
  border: 2px solid ${(props) => props.theme.colors.primary};
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: ${(props) => props.theme.spacing.sm};
`;

const LoadingText = styled.p`
  color: ${(props) => props.theme.colors.text.secondary};
  font-size: ${(props) => props.theme.typography.fontSize.base};
  margin: 0;
`;

const ErrorContainer = styled.div`
  text-align: center;
  padding: ${(props) => props.theme.spacing.xxl} 0;
`;

const ErrorBox = styled.div`
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: ${(props) => props.theme.borderRadius.lg};
  padding: ${(props) => props.theme.spacing.md};
  max-width: 500px;
  margin: 0 auto;
`;

const ErrorText = styled.p`
  color: #dc2626;
  margin: ${(props) => props.theme.spacing.xs} 0;
  font-weight: ${(props) => props.theme.typography.fontWeight.medium};
`;

const ErrorSubtext = styled.p`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: ${(props) => props.theme.spacing.xs} 0;
  font-size: ${(props) => props.theme.typography.fontSize.sm};
`;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${(props) => props.theme.spacing.xxl};
`;

const Section = styled.div`
  margin-bottom: ${(props) => props.theme.spacing.xxl};
`;

const SectionTitle = styled.h3`
  font-size: ${(props) => props.theme.typography.fontSize["2xl"]};
  font-weight: ${(props) => props.theme.typography.fontWeight.semibold};
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: ${(props) => props.theme.spacing.sm};
  position: relative;
`;

const VideoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: ${(props) => props.theme.spacing.md};

  @media (min-width: ${(props) => props.theme.breakpoints.sm}) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: ${(props) => props.theme.breakpoints.md}) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (min-width: ${(props) => props.theme.breakpoints.lg}) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (min-width: ${(props) => props.theme.breakpoints.xl}) {
    grid-template-columns: repeat(5, 1fr);
  }
`;

// Ads Styled Components
const AdsSection = styled.div`
  margin-bottom: ${(props) => props.theme.spacing.xxl};
`;

const FeaturedAds = styled.div`
  margin-bottom: ${(props) => props.theme.spacing.md};
`;

const BannerAds = styled.div`
  margin-bottom: ${(props) => props.theme.spacing.md};
`;

const AdsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${(props) => props.theme.spacing.sm};
`;

const BannerGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${(props) => props.theme.spacing.sm};

  @media (min-width: ${(props) => props.theme.breakpoints.md}) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const AdLink = styled.a`
  display: block;
  text-decoration: none;
  color: inherit;
`;

const AdCard = styled.div`
  position: relative;
  overflow: hidden;
  border-radius: ${(props) => props.theme.borderRadius.lg};
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease, transform 0.3s ease;

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
  }
`;

const AdImage = styled.img`
  width: 100%;
  height: 4rem; /* h-16 - for 980x60 format */
  object-fit: cover;
  transition: transform 0.3s ease;

  ${AdCard}:hover & {
    transform: scale(1.05);
  }
`;

const BannerImage = styled.img`
  width: 100%;
  height: 16rem; /* h-64 - for 960x400 format */
  object-fit: cover;
  transition: transform 0.3s ease;

  ${AdCard}:hover & {
    transform: scale(1.05);
  }
`;

const AdOverlay = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: ${(props) => props.theme.spacing.xs};
`;

const AdTitle = styled.h4`
  color: white;
  font-weight: ${(props) => props.theme.typography.fontWeight.medium};
  font-size: ${(props) => props.theme.typography.fontSize.xs};
  margin: 0;
`;

const Homepage: React.FC<HomepageProps> = () => {
  const { data: apiData, isLoading, error } = useHomeData();
  const { data: adsData } = useAdsData();

  if (isLoading) {
    return (
      <Container>
        <LoadingContainer>
          <Spinner />
          <LoadingText>Loading content...</LoadingText>
        </LoadingContainer>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <ErrorContainer>
          <ErrorBox>
            <ErrorText>Error loading content: {error.message}</ErrorText>
            <ErrorSubtext>Showing fallback content...</ErrorSubtext>
          </ErrorBox>
        </ErrorContainer>
      </Container>
    );
  }

  return (
    <Container>
      {/* Ads Section */}
      {adsData?.data && (
        <AdsSection>
          {/* Horizontal Ads */}
          {adsData.data.ads?.shouyehengfu &&
            adsData.data.ads.shouyehengfu.length > 0 && (
              <FeaturedAds>
                <AdsGrid>
                  {adsData.data.ads.shouyehengfu
                    .filter((ad) => ad.status === 1)
                    .map((ad, index) => (
                      <AdLink
                        key={index}
                        href={ad.url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <AdCard>
                          <Image
                            src={ad.local_img || ad.img}
                            alt={ad.name}
                            className={AdImage}
                            width="100%"
                            height="4rem"
                          />
                          <AdOverlay>
                            <AdTitle>{ad.name}</AdTitle>
                          </AdOverlay>
                        </AdCard>
                      </AdLink>
                    ))}
                </AdsGrid>
              </FeaturedAds>
            )}

          {/* Banner Ads */}
          {adsData.data.banners && adsData.data.banners.length > 0 && (
            <BannerAds>
              <BannerGrid>
                {adsData.data.banners.map((banner, index) => (
                  <AdLink
                    key={index}
                    href={banner.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <AdCard>
                      <Image
                        src={banner.local_img || banner.img}
                        alt={banner.title}
                        className={BannerImage}
                        width="100%"
                        height="16rem"
                      />
                      <AdOverlay>
                        <AdTitle>{banner.title}</AdTitle>
                      </AdOverlay>
                    </AdCard>
                  </AdLink>
                ))}
              </BannerGrid>
            </BannerAds>
          )}
        </AdsSection>
      )}

      {/* API Content Section */}
      {apiData?.data && (
        <ContentContainer>
          {Object.entries(apiData.data)
            .filter(([key]) => key !== "seo" && key !== "notice")
            .map(([, value], i) => {
              if (!value || !value.data || value.data.length === 0) {
                return null;
              }

              const title =
                value.channel !== "topic"
                  ? SECTION_NAMES[i]
                  : value.data[0]?.title;
              const listData =
                value.channel === "topic" ? value.data[0]?.list : value.data;

              return (
                <Section key={i}>
                  <SectionTitle>{title}</SectionTitle>
                  <VideoGrid>
                    {listData.map((item: any) => (
                      <VideoCard
                        key={item.id}
                        id={item.id}
                        title={item.title}
                        performer={item.publisher || ""}
                        thumb={item.thumb}
                        duration={item.duration}
                        insert_time={item.insert_time}
                        isVip={item.channel === "vip"}
                      />
                    ))}
                  </VideoGrid>
                </Section>
              );
            })}
        </ContentContainer>
      )}
    </Container>
  );
};

export default Homepage;
