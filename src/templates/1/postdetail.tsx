import React from "react";
import { useParams } from "react-router-dom";
import VideoPlayer from "../../components/VideoPlayer";
import { useVideoDetail } from "../../hooks/useApi";

interface Ad {
  id: number;
  site: number;
  name: string;
  img: string;
  local_img: string;
  url: string;
  position: number;
  sort: number;
  insert_time: number;
  update_time: number;
  status: number;
  is_show: number;
  rmk: null;
  remote: number;
}

interface Info {
  id: number;
  title: string;
  tags: string;
  channel: string;
  insert_time: number;
  update_time: number;
  thumb: string;
  video_url: string;
  down_url: string;
  actors: null;
  keywords: null;
  description: string;
  sync_bbs: number;
  thumb_series: string;
  thumb_ori: null;
  jump_name: string;
  thumb_series_replace: string[];
}

interface Source {
  gao_img: string;
  gao_url: string;
  isImgGao: boolean;
  isVideoGao: boolean;
  m3u8_host: string;
  m3u8_host1: string;
  m3u8_host2: string;
  video_url: string;
  thumb: string;
  channel: string;
  down_url: string;
  playId: string;
  nav_id: number;
}

interface VideoDetail {
  ad: Ad;
  info: Info;
  playId: string;
  source: Source;
}

const PostDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const { data: videoDetail, isLoading, error } = useVideoDetail(id || "");

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4">
          <div className="animate-pulse">
            <div className="bg-gray-300 h-64 rounded-lg mb-6"></div>
            <div className="bg-gray-300 h-8 rounded mb-4"></div>
            <div className="bg-gray-300 h-4 rounded mb-2"></div>
            <div className="bg-gray-300 h-4 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !videoDetail) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="text-red-500 mb-4">
              <svg
                className="w-16 h-16 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              视频未找到
            </h2>
            <p className="text-gray-600">
              抱歉，您请求的视频不存在或已被删除。
            </p>
          </div>
        </div>
      </div>
    );
  }

  const video = videoDetail?.data as VideoDetail;
  console.log("video", video);
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Video Player Section */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="aspect-video">
            <VideoPlayer
              videoUrl={video.source?.video_url || video.info?.video_url || ""}
              source={
                video.source?.m3u8_host ||
                video.source?.m3u8_host1 ||
                video.source?.m3u8_host2
              }
              poster={video.source?.thumb || video.info?.thumb}
              className="w-full h-full"
              onReady={(player) => {
                console.log("Video player ready:", player);
              }}
              onError={(error) => {
                console.error("Video player error:", error);
              }}
            />
          </div>
        </div>

        {/* Video Info Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {video.info?.title}
          </h1>

          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
            {video.info?.insert_time && (
              <span className="flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                    clipRule="evenodd"
                  />
                </svg>
                {new Date(video.info.insert_time * 1000).toLocaleDateString()}
              </span>
            )}

            {video.info?.channel && (
              <span className="flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path
                    fillRule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clipRule="evenodd"
                  />
                </svg>
                {video.info.channel}
              </span>
            )}
          </div>

          {video.info?.description && (
            <div className="prose max-w-none">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                视频描述
              </h3>
              <p className="text-gray-700 leading-relaxed">
                {video.info.description}
              </p>
            </div>
          )}

          {video.info?.tags && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">标签</h3>
              <div className="flex flex-wrap gap-2">
                {video.info.tags.split(",").map((tag, index) => (
                  <span
                    key={index}
                    className="inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full"
                  >
                    {tag.trim()}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Related Videos Section */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">相关视频</h3>
          <div className="text-gray-600">
            <p>相关视频功能即将推出...</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostDetail;
