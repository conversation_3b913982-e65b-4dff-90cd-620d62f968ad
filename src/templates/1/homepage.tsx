import React from "react";
import { HomepageProps } from "../../types/index";
import { useAdsData, useHomeData } from "../../hooks/useApi";
import VideoCard from "../../components/VideoCard";
import Image from "../../components/Image";
import { SECTION_NAMES } from "../../constants/sections";

const Homepage: React.FC<HomepageProps> = () => {
  const { data: apiData, isLoading, error } = useHomeData();
  const { data: adsData } = useAdsData();
  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto px-4 lg:px-8 py-12">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mb-4"></div>
          <p className="text-gray-500">Loading content...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto px-4 lg:px-8 py-12">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <p className="text-red-600">
              Error loading content: {error.message}
            </p>
            <p className="text-gray-500 mt-2">Showing fallback content...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 lg:px-8 py-12">
      {/* Ads Section */}
      {adsData?.data && (
        <div className="mb-16">
          {/* Horizontal Ads */}
          {adsData.data.ads?.shouyehengfu &&
            adsData.data.ads.shouyehengfu.length > 0 && (
              <div className="mb-8">
                <div className="grid grid-cols-1 gap-4">
                  {adsData.data.ads.shouyehengfu
                    .filter((ad) => ad.status === 1) // Only show active ads
                    .map((ad, index) => (
                      <a
                        key={index}
                        href={ad.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block group"
                      >
                        <div className="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                          <Image
                            src={ad.local_img || ad.img}
                            alt={ad.name}
                            className="w-full h-16 object-cover group-hover:scale-105 transition-transform duration-300"
                            width="100%"
                            height="4rem"
                          />

                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
                            <h4 className="text-white font-medium text-xs">
                              {ad.name}
                            </h4>
                          </div>
                        </div>
                      </a>
                    ))}
                </div>
              </div>
            )}

          {/* Banner Ads */}
          {adsData.data.banners && adsData.data.banners.length > 0 && (
            <div className="mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {adsData.data.banners.map((banner, index) => (
                  <a
                    key={index}
                    href={banner.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block group"
                  >
                    <div className="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                      <Image
                        src={banner.local_img || banner.img}
                        alt={banner.title}
                        className="w-full object-cover group-hover:scale-105 transition-transform duration-300"
                        width="100%"
                        height="16rem"
                      />

                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                        <h4 className="text-white font-medium text-sm">
                          {banner.title}
                        </h4>
                      </div>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* API Content Section */}
      {apiData?.data && (
        <div className="space-y-16">
          {Object.entries(apiData.data)
            .filter(([key]) => key !== "seo" && key !== "notice")
            .map(([, value], i) => {
              if (!value || !value.data || value.data.length === 0) {
                return null;
              }

              const title =
                value.channel !== "topic"
                  ? SECTION_NAMES[i]
                  : value.data[0]?.title;
              const listData =
                value.channel === "topic" ? value.data[0]?.list : value.data;

              return (
                <div key={i} className="mb-16">
                  <h3
                    className="text-2xl font-semibold text-gray-900 mb-4"
                    style={{
                      fontSize: "1.5rem",
                      fontWeight: "600",
                      color: "#111827",
                      marginBottom: "1rem",
                    }}
                  >
                    {title}
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                    {listData.map((item: any) => (
                      <VideoCard
                        key={item.id}
                        id={item.id}
                        title={item.title}
                        performer={item.publisher || ""}
                        thumb={item.thumb}
                        duration={item.duration}
                        insert_time={item.insert_time}
                        isVip={item.channel === "vip"}
                      />
                    ))}
                  </div>
                </div>
              );
            })}
        </div>
      )}
    </div>
  );
};

export default Homepage;
