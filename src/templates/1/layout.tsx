import React from "react";
import { <PERSON> } from "react-router-dom";
import { LayoutProps } from "../../types/index";
import { HEADER_MENU } from "../../constants/menu";
import { SITE_SETTINGS } from "../../constants/site";

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50 font-serif flex flex-col">
      <header
        className="shadow-sm border-b border-gray-200"
        style={{
          background: "linear-gradient(90deg, #FB8EB7, #EAA6C7, #D9BFD9)",
        }}
      >
        <div className="max-w-6xl mx-auto px-4 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link to="/" className="block">
                <img
                  src="/logo.png"
                  alt={SITE_SETTINGS.title}
                  className="h-10 w-auto cursor-pointer hover:opacity-80 transition-opacity"
                />
              </Link>
            </div>
            <nav className="hidden md:flex space-x-8">
              {HEADER_MENU.map((item, index) => (
                <button
                  key={index}
                  onClick={() => {
                    // Handle navigation based on channel or code
                  }}
                  className="text-white hover:text-gray-200 transition-colors duration-300 bg-transparent border-none cursor-pointer"
                >
                  {item.name}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </header>

      <main className="flex-1">{children}</main>

      <footer className="bg-gray-800 text-white py-12">
        <div className="max-w-6xl mx-auto px-4 lg:px-8 text-center">
          <p className="text-gray-300 m-0">
            © 2024 {SITE_SETTINGS.title}. 高质量视频内容平台.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
