/** @jsxImportSource @emotion/react */
import React from "react";
import { Link } from "react-router-dom";
import { css } from "@emotion/react";
import { LayoutProps } from "../../types/index";
import { HEADER_MENU } from "../../constants/menu";
import { SITE_SETTINGS } from "../../constants/site";
import { tokens, containerStyles, buttonStyles } from "./styles";

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div
      css={css`
        min-height: 100vh;
        background-color: ${tokens.colors.background};
        font-family: ${tokens.typography.fontFamily};
        display: flex;
        flex-direction: column;
      `}
    >
      <header
        css={css`
          background: linear-gradient(
            90deg,
            ${tokens.colors.primary},
            ${tokens.colors.secondary},
            ${tokens.colors.tertiary}
          );
          box-shadow: ${tokens.shadows.base};
          border-bottom: 1px solid ${tokens.colors.border};
        `}
      >
        <div css={containerStyles}>
          <div
            css={css`
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: ${tokens.spacing[6]} 0;
            `}
          >
            <div>
              <Link
                to="/"
                css={css`
                  display: block;
                  transition: opacity 0.2s ease;

                  &:hover {
                    opacity: 0.8;
                  }
                `}
              >
                <img
                  src="/logo.png"
                  alt={SITE_SETTINGS.title}
                  css={css`
                    height: 2.5rem;
                    width: auto;
                    cursor: pointer;
                  `}
                />
              </Link>
            </div>
            <nav
              css={css`
                display: none;

                @media (min-width: ${tokens.breakpoints.md}) {
                  display: flex;
                  gap: ${tokens.spacing[8]};
                }
              `}
            >
              {HEADER_MENU.map((item, index) => (
                <button
                  key={index}
                  css={css`
                    ${buttonStyles}
                    color: white;
                    font-size: ${tokens.typography.fontSize.base};
                    font-weight: ${tokens.typography.fontWeight.medium};
                    padding: ${tokens.spacing[2]} ${tokens.spacing[4]};
                    border-radius: ${tokens.borderRadius.md};

                    &:hover {
                      background-color: rgba(255, 255, 255, 0.1);
                      transform: translateY(-1px);
                    }
                  `}
                >
                  {item.name}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </header>

      <main
        css={css`
          flex: 1;
          padding: ${tokens.spacing[12]} 0;
        `}
      >
        {children}
      </main>

      <footer
        css={css`
          background-color: ${tokens.colors.footer};
          color: white;
          padding: ${tokens.spacing[12]} 0;
          margin-top: auto;
        `}
      >
        <div css={containerStyles}>
          <div
            css={css`
              text-align: center;
            `}
          >
            <p
              css={css`
                color: #d1d5db;
                font-size: ${tokens.typography.fontSize.sm};
                margin: 0;
              `}
            >
              © 2024 {SITE_SETTINGS.title}. 高质量视频内容平台.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
