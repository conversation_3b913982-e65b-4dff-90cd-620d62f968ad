import { css } from "@emotion/react";

// Design tokens
export const tokens = {
  colors: {
    primary: "#FB8EB7",
    secondary: "#EAA6C7",
    tertiary: "#D9BFD9",
    background: "#f9fafb",
    surface: "#ffffff",
    text: {
      primary: "#111827",
      secondary: "#6b7280",
      muted: "#9ca3af",
    },
    border: "#e5e7eb",
    error: "#dc2626",
    errorBg: "#fef2f2",
    errorBorder: "#fecaca",
    footer: "#1f2937",
  },
  spacing: {
    1: "0.25rem",
    2: "0.5rem",
    3: "0.75rem",
    4: "1rem",
    6: "1.5rem",
    8: "2rem",
    12: "3rem",
    16: "4rem",
  },
  typography: {
    fontFamily: "Georgia, serif",
    fontSize: {
      sm: "0.875rem",
      base: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
  },
  breakpoints: {
    sm: "640px",
    md: "768px",
    lg: "1024px",
    xl: "1280px",
  },
  shadows: {
    sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    base: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
    md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
  },
  borderRadius: {
    sm: "0.25rem",
    base: "0.375rem",
    md: "0.5rem",
    lg: "0.75rem",
    xl: "1rem",
  },
};

// Common CSS patterns
export const containerStyles = css`
  max-width: 1152px;
  margin: 0 auto;
  padding: 0 ${tokens.spacing[4]};

  @media (min-width: ${tokens.breakpoints.lg}) {
    padding: 0 ${tokens.spacing[8]};
  }
`;

export const cardStyles = css`
  background-color: ${tokens.colors.surface};
  border-radius: ${tokens.borderRadius.lg};
  box-shadow: ${tokens.shadows.base};
  overflow: hidden;
`;

export const buttonStyles = css`
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }
`;

export const gridResponsive = css`
  display: grid;
  gap: ${tokens.spacing[6]};
  grid-template-columns: repeat(1, 1fr);

  @media (min-width: ${tokens.breakpoints.sm}) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: ${tokens.breakpoints.md}) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (min-width: ${tokens.breakpoints.lg}) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (min-width: ${tokens.breakpoints.xl}) {
    grid-template-columns: repeat(5, 1fr);
  }
`;

export const fadeInAnimation = css`
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  animation: fadeIn 0.6s ease-out;
`;

export const spinAnimation = css`
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  animation: spin 1s linear infinite;
`;
