/** @jsxImportSource @emotion/react */
import React from "react";
import { css } from "@emotion/react";
import { HomepageProps } from "../../types/index";
import { useHomeData, useAdsData } from "../../hooks/useApi";
import VideoCard from "../../components/VideoCard";
import Image from "../../components/Image";
import { SECTION_NAMES } from "../../constants/sections";
import {
  tokens,
  containerStyles,
  gridResponsive,
  fadeInAnimation,
  spinAnimation,
} from "./styles";

const Homepage: React.FC<HomepageProps> = () => {
  const { data: apiData, isLoading, error } = useHomeData();
  const { data: adsData } = useAdsData();

  if (isLoading) {
    return (
      <div css={containerStyles}>
        <div
          css={css`
            text-align: center;
            padding: ${tokens.spacing[16]} 0;
          `}
        >
          <div
            css={css`
              display: inline-block;
              width: 3rem;
              height: 3rem;
              border: 2px solid ${tokens.colors.primary};
              border-top: 2px solid transparent;
              border-radius: 50%;
              margin-bottom: ${tokens.spacing[4]};
              ${spinAnimation}
            `}
          />
          <p
            css={css`
              color: ${tokens.colors.text.secondary};
              font-size: ${tokens.typography.fontSize.base};
              margin: 0;
            `}
          >
            Loading content...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div css={containerStyles}>
        <div
          css={css`
            text-align: center;
            padding: ${tokens.spacing[16]} 0;
          `}
        >
          <div
            css={css`
              background-color: ${tokens.colors.errorBg};
              border: 1px solid ${tokens.colors.errorBorder};
              border-radius: ${tokens.borderRadius.lg};
              padding: ${tokens.spacing[6]};
              max-width: 500px;
              margin: 0 auto;
            `}
          >
            <p
              css={css`
                color: ${tokens.colors.error};
                margin: ${tokens.spacing[2]} 0;
                font-weight: ${tokens.typography.fontWeight.medium};
              `}
            >
              Error loading content: {error.message}
            </p>
            <p
              css={css`
                color: ${tokens.colors.text.secondary};
                margin: ${tokens.spacing[2]} 0;
                font-size: ${tokens.typography.fontSize.sm};
              `}
            >
              Showing fallback content...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      css={css`
        ${containerStyles}
        ${fadeInAnimation}
      `}
    >
      {/* Ads Section */}
      {adsData?.data && (
        <div
          css={css`
            margin-bottom: ${tokens.spacing[16]};
          `}
        >
          {/* Horizontal Ads */}
          {adsData.data.ads?.shouyehengfu &&
            adsData.data.ads.shouyehengfu.length > 0 && (
              <div
                css={css`
                  margin-bottom: ${tokens.spacing[8]};
                `}
              >
                <div
                  css={css`
                    display: grid;
                    grid-template-columns: 1fr;
                    gap: ${tokens.spacing[4]};
                  `}
                >
                  {adsData.data.ads.shouyehengfu
                    .filter((ad) => ad.status === 1)
                    .map((ad, index) => (
                      <a
                        key={index}
                        href={ad.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        css={css`
                          display: block;
                          text-decoration: none;
                          color: inherit;
                        `}
                      >
                        <div
                          css={css`
                            position: relative;
                            overflow: hidden;
                            border-radius: ${tokens.borderRadius.lg};
                            box-shadow: ${tokens.shadows.md};
                            transition: box-shadow 0.3s ease,
                              transform 0.3s ease;

                            &:hover {
                              box-shadow: ${tokens.shadows.lg};
                              transform: scale(1.02);
                            }
                          `}
                        >
                          <Image
                            src={ad.local_img || ad.img}
                            alt={ad.name}
                            width="100%"
                            height="4rem"
                            css={css`
                              width: 100%;
                              height: 4rem; /* h-16 - for 980x60 format */
                              object-fit: cover;
                              transition: transform 0.3s ease;
                            `}
                          />
                          <div
                            css={css`
                              position: absolute;
                              bottom: 0;
                              left: 0;
                              right: 0;
                              background: linear-gradient(
                                to top,
                                rgba(0, 0, 0, 0.8),
                                transparent
                              );
                              padding: ${tokens.spacing[2]};
                            `}
                          >
                            <h4
                              css={css`
                                color: white;
                                font-weight: ${tokens.typography.fontWeight
                                  .medium};
                                font-size: ${tokens.typography.fontSize.sm};
                                margin: 0;
                              `}
                            >
                              {ad.name}
                            </h4>
                          </div>
                        </div>
                      </a>
                    ))}
                </div>
              </div>
            )}

          {/* Banner Ads */}
          {adsData.data.banners && adsData.data.banners.length > 0 && (
            <div
              css={css`
                margin-bottom: ${tokens.spacing[8]};
              `}
            >
              <div
                css={css`
                  display: grid;
                  grid-template-columns: 1fr;
                  gap: ${tokens.spacing[4]};

                  @media (min-width: ${tokens.breakpoints.md}) {
                    grid-template-columns: repeat(2, 1fr);
                  }
                `}
              >
                {adsData.data.banners.map((banner, index) => (
                  <a
                    key={index}
                    href={banner.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    css={css`
                      display: block;
                      text-decoration: none;
                      color: inherit;
                    `}
                  >
                    <div
                      css={css`
                        position: relative;
                        overflow: hidden;
                        border-radius: ${tokens.borderRadius.lg};
                        box-shadow: ${tokens.shadows.md};
                        transition: box-shadow 0.3s ease, transform 0.3s ease;

                        &:hover {
                          box-shadow: ${tokens.shadows.lg};
                          transform: scale(1.02);
                        }
                      `}
                    >
                      <Image
                        src={banner.local_img || banner.img}
                        alt={banner.title}
                        width="100%"
                        height="16rem"
                        css={css`
                          width: 100%;
                          height: 16rem; /* h-64 - for 960x400 format */
                          object-fit: cover;
                          transition: transform 0.3s ease;
                        `}
                      />
                      <div
                        css={css`
                          position: absolute;
                          bottom: 0;
                          left: 0;
                          right: 0;
                          background: linear-gradient(
                            to top,
                            rgba(0, 0, 0, 0.8),
                            transparent
                          );
                          padding: ${tokens.spacing[2]};
                        `}
                      >
                        <h4
                          css={css`
                            color: white;
                            font-weight: ${tokens.typography.fontWeight.medium};
                            font-size: ${tokens.typography.fontSize.sm};
                            margin: 0;
                          `}
                        >
                          {banner.title}
                        </h4>
                      </div>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* API Content Section */}
      {apiData?.data && (
        <div
          css={css`
            display: flex;
            flex-direction: column;
            gap: ${tokens.spacing[16]};
          `}
        >
          {Object.entries(apiData.data)
            .filter(([key]) => key !== "seo" && key !== "notice")
            .map(([, value], i) => {
              if (!value || !value.data || value.data.length === 0) {
                return null;
              }

              const title =
                value.channel !== "topic"
                  ? SECTION_NAMES[i]
                  : value.data[0]?.title;
              const listData =
                value.channel === "topic" ? value.data[0]?.list : value.data;

              return (
                <div
                  key={i}
                  css={css`
                    margin-bottom: ${tokens.spacing[16]};
                  `}
                >
                  <h3
                    css={css`
                      font-size: ${tokens.typography.fontSize["2xl"]};
                      font-weight: ${tokens.typography.fontWeight.semibold};
                      color: ${tokens.colors.text.primary};
                      margin-bottom: ${tokens.spacing[4]};
                      position: relative;
                    `}
                  >
                    {title}
                  </h3>
                  <div css={gridResponsive}>
                    {listData.map((item: any) => (
                      <VideoCard
                        key={item.id}
                        id={item.id}
                        title={item.title}
                        performer={item.publisher || ""}
                        thumb={item.thumb}
                        duration={item.duration}
                        insert_time={item.insert_time}
                        isVip={item.channel === "vip"}
                      />
                    ))}
                  </div>
                </div>
              );
            })}
        </div>
      )}
    </div>
  );
};

export default Homepage;
