/** @jsxImportSource @emotion/react */
import React from "react";
import { useParams } from "react-router-dom";
import { css } from "@emotion/react";
import VideoPlayer from "../../components/VideoPlayer";
import { useVideoDetail } from "../../hooks/useApi";
import { tokens, containerStyles, cardStyles } from "./styles";

const PostDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { data: videoData, isLoading, error } = useVideoDetail(id || "");

  if (isLoading) {
    return (
      <div css={containerStyles}>
        <div
          css={css`
            text-align: center;
            padding: ${tokens.spacing[16]} 0;
            color: ${tokens.colors.text.secondary};
          `}
        >
          <p>Loading video details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div css={containerStyles}>
        <div
          css={css`
            text-align: center;
            padding: ${tokens.spacing[16]} 0;
            color: ${tokens.colors.error};
          `}
        >
          <p>Error loading video: {error.message}</p>
        </div>
      </div>
    );
  }

  if (!videoData?.data) {
    return (
      <div css={containerStyles}>
        <div
          css={css`
            text-align: center;
            padding: ${tokens.spacing[16]} 0;
            color: ${tokens.colors.error};
          `}
        >
          <p>Video not found</p>
        </div>
      </div>
    );
  }

  const video = videoData.data;

  return (
    <div css={containerStyles}>
      <div
        css={css`
          margin-bottom: ${tokens.spacing[12]};
        `}
      >
        <div
          css={css`
            ${cardStyles}
            box-shadow: ${tokens.shadows.lg};
          `}
        >
          <VideoPlayer
            videoUrl={video.source?.video_url || video.info?.video_url || ""}
            source={
              video.source?.m3u8_host ||
              video.source?.m3u8_host1 ||
              video.source?.m3u8_host2
            }
            poster={video.source?.thumb || video.info?.thumb}
          />
        </div>
      </div>

      <div
        css={css`
          ${cardStyles}
          padding: ${tokens.spacing[12]};
        `}
      >
        <h1
          css={css`
            font-size: ${tokens.typography.fontSize["3xl"]};
            font-weight: ${tokens.typography.fontWeight.bold};
            color: ${tokens.colors.text.primary};
            margin-bottom: ${tokens.spacing[6]};
            line-height: 1.2;
          `}
        >
          {video.info?.title || "Untitled Video"}
        </h1>

        <div
          css={css`
            display: flex;
            flex-wrap: wrap;
            gap: ${tokens.spacing[6]};
            margin-bottom: ${tokens.spacing[8]};
            padding-bottom: ${tokens.spacing[8]};
            border-bottom: 1px solid ${tokens.colors.border};
          `}
        >
          {video.info?.publisher && (
            <div
              css={css`
                display: flex;
                align-items: center;
                gap: ${tokens.spacing[2]};
              `}
            >
              <span
                css={css`
                  font-weight: ${tokens.typography.fontWeight.medium};
                  color: ${tokens.colors.text.secondary};
                  font-size: ${tokens.typography.fontSize.sm};
                `}
              >
                Publisher:
              </span>
              <span
                css={css`
                  color: ${tokens.colors.text.primary};
                  font-size: ${tokens.typography.fontSize.sm};
                `}
              >
                {video.info.publisher}
              </span>
            </div>
          )}
          {video.info?.duration && (
            <div
              css={css`
                display: flex;
                align-items: center;
                gap: ${tokens.spacing[2]};
              `}
            >
              <span
                css={css`
                  font-weight: ${tokens.typography.fontWeight.medium};
                  color: ${tokens.colors.text.secondary};
                  font-size: ${tokens.typography.fontSize.sm};
                `}
              >
                Duration:
              </span>
              <span
                css={css`
                  color: ${tokens.colors.text.primary};
                  font-size: ${tokens.typography.fontSize.sm};
                `}
              >
                {video.info.duration}
              </span>
            </div>
          )}
          {video.info?.insert_time && (
            <div
              css={css`
                display: flex;
                align-items: center;
                gap: ${tokens.spacing[2]};
              `}
            >
              <span
                css={css`
                  font-weight: ${tokens.typography.fontWeight.medium};
                  color: ${tokens.colors.text.secondary};
                  font-size: ${tokens.typography.fontSize.sm};
                `}
              >
                Published:
              </span>
              <span
                css={css`
                  color: ${tokens.colors.text.primary};
                  font-size: ${tokens.typography.fontSize.sm};
                `}
              >
                {video.info.insert_time}
              </span>
            </div>
          )}
        </div>

        {video.info?.description && (
          <div
            css={css`
              color: ${tokens.colors.text.primary};
              line-height: 1.6;
              font-size: ${tokens.typography.fontSize.base};
            `}
          >
            <p>{video.info.description}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PostDetail;
