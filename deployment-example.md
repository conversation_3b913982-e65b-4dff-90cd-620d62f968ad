# Deployment Configuration Example

This document shows how to deploy each template to different CDN paths or subdomains.

## Build Output Structure

After running `npm run build`, you'll have:

```
dist/
├── template1/          # Tailwind CSS Template
│   ├── index.html
│   ├── assets/
│   └── ...
├── template2/          # CSS Modules Template
│   ├── index.html
│   ├── assets/
│   └── ...
└── template3/          # Inline Styles Template
    ├── index.html
    ├── assets/
    └── ...
```

## CDN Deployment Examples

### Option 1: Different CDN Paths

```
https://your-cdn.com/template1/    # Tailwind CSS
https://your-cdn.com/template2/    # CSS Modules
https://your-cdn.com/template3/    # Inline Styles
```

### Option 2: Different Subdomains

```
https://tailwind.yourdomain.com/   # Template 1
https://cssmodules.yourdomain.com/ # Template 2
https://inline.yourdomain.com/     # Template 3
```

### Option 3: Different Domains

```
https://template1.yourdomain.com/  # Tailwind CSS
https://template2.yourdomain.com/  # CSS Modules
https://template3.yourdomain.com/  # Inline Styles
```

## Nginx Configuration Example

```nginx
# Template 1 - Tailwind CSS
location /template1/ {
    alias /var/www/dist/template1/;
    try_files $uri $uri/ /template1/index.html;
}

# Template 2 - CSS Modules
location /template2/ {
    alias /var/www/dist/template2/;
    try_files $uri $uri/ /template2/index.html;
}

# Template 3 - Inline Styles
location /template3/ {
    alias /var/www/dist/template3/;
    try_files $uri $uri/ /template3/index.html;
}
```

## AWS S3 + CloudFront Example

1. Upload each template to a different S3 bucket or folder
2. Create separate CloudFront distributions for each template
3. Configure different origins for each template

## Vercel Deployment Example

```json
{
  "builds": [
    {
      "src": "dist/template1/**",
      "use": "@vercel/static"
    },
    {
      "src": "dist/template2/**",
      "use": "@vercel/static"
    },
    {
      "src": "dist/template3/**",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/template1/(.*)",
      "dest": "/dist/template1/$1"
    },
    {
      "src": "/template2/(.*)",
      "dest": "/dist/template2/$1"
    },
    {
      "src": "/template3/(.*)",
      "dest": "/dist/template3/$1"
    }
  ]
}
```

## Environment-Based Template Selection

You can also use environment variables to determine which template to build:

```bash
# Build specific template based on environment
TEMPLATE_TYPE=1 npm run build:template1
TEMPLATE_TYPE=2 npm run build:template2
TEMPLATE_TYPE=3 npm run build:template3
```

## CI/CD Pipeline Example

```yaml
# GitHub Actions example
name: Build Templates
on: [push]

jobs:
  build-template1:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: npm install
      - run: npm run build:template1
      - run: aws s3 sync dist/template1/ s3://your-bucket/template1/

  build-template2:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: npm install
      - run: npm run build:template2
      - run: aws s3 sync dist/template2/ s3://your-bucket/template2/

  build-template3:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: npm install
      - run: npm run build:template3
      - run: aws s3 sync dist/template3/ s3://your-bucket/template3/
```
