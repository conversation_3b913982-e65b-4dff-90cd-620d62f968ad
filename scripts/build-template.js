#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get available templates by scanning the templates directory
function getAvailableTemplates() {
  const templatesDir = path.join(__dirname, '../src/templates');
  try {
    return fs.readdirSync(templatesDir)
      .filter(item => {
        const itemPath = path.join(templatesDir, item);
        return fs.statSync(itemPath).isDirectory();
      })
      .sort();
  } catch (error) {
    console.error('Error reading templates directory:', error);
    return [];
  }
}

// Build a specific template
function buildTemplate(templateId) {
  console.log(`\n🔨 Building template ${templateId}...`);
  
  try {
    // TypeScript compilation
    execSync(`TEMPLATE_TYPE=template${templateId} npx tsc`, { 
      stdio: 'inherit',
      env: { ...process.env, TEMPLATE_TYPE: `template${templateId}` }
    });
    
    // Vite build
    execSync(`TEMPLATE_TYPE=template${templateId} npx vite build`, { 
      stdio: 'inherit',
      env: { ...process.env, TEMPLATE_TYPE: `template${templateId}` }
    });
    
    // Post-build: Copy HTML to correct location
    const distDir = `dist/template${templateId}`;
    const possiblePaths = [
      `${distDir}/public/template${templateId}/index.html`,
      `${distDir}/template${templateId}/index.html`
    ];
    
    for (const sourcePath of possiblePaths) {
      if (fs.existsSync(sourcePath)) {
        const targetPath = `${distDir}/index.html`;
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`✅ HTML file copied to ${targetPath}`);
        break;
      }
    }
    
    console.log(`✅ Template ${templateId} built successfully!`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to build template ${templateId}:`, error.message);
    return false;
  }
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    const templates = getAvailableTemplates();
    console.log('📋 Available templates:');
    templates.forEach(template => console.log(`  - ${template}`));
    console.log('\n💡 Usage: npm run build:template <template_id>');
    console.log('💡 Example: npm run build:template 1');
    return;
  }
  
  const templateId = args[0];
  const templates = getAvailableTemplates();
  
  if (!templates.includes(templateId)) {
    console.error(`❌ Template "${templateId}" not found.`);
    console.log('📋 Available templates:', templates.join(', '));
    process.exit(1);
  }
  
  buildTemplate(templateId);
}

main();
