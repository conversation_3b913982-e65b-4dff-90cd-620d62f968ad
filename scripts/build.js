#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get available templates by scanning the templates directory
function getAvailableTemplates() {
  const templatesDir = path.join(__dirname, '../src/templates');
  try {
    return fs.readdirSync(templatesDir)
      .filter(item => {
        const itemPath = path.join(templatesDir, item);
        return fs.statSync(itemPath).isDirectory();
      })
      .sort();
  } catch (error) {
    console.error('Error reading templates directory:', error);
    return [];
  }
}

// Build a specific template
function buildTemplate(templateId) {
  console.log(`\n🔨 Building template ${templateId}...`);
  
  try {
    // TypeScript compilation
    execSync(`TEMPLATE_TYPE=template${templateId} npx tsc`, { 
      stdio: 'inherit',
      env: { ...process.env, TEMPLATE_TYPE: `template${templateId}` }
    });
    
    // Vite build
    execSync(`TEMPLATE_TYPE=template${templateId} npx vite build`, { 
      stdio: 'inherit',
      env: { ...process.env, TEMPLATE_TYPE: `template${templateId}` }
    });
    
    // Post-build: Copy HTML to correct location
    const distDir = `dist/template${templateId}`;
    const possiblePaths = [
      `${distDir}/public/template${templateId}/index.html`,
      `${distDir}/template${templateId}/index.html`
    ];
    
    for (const sourcePath of possiblePaths) {
      if (fs.existsSync(sourcePath)) {
        const targetPath = `${distDir}/index.html`;
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`✅ HTML file copied to ${targetPath}`);
        break;
      }
    }
    
    console.log(`✅ Template ${templateId} built successfully!`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to build template ${templateId}:`, error.message);
    return false;
  }
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  
  // If specific template is provided
  if (args.length > 0) {
    const templateId = args[0];
    console.log(`Building specific template: ${templateId}`);
    buildTemplate(templateId);
    return;
  }
  
  // Build all available templates
  const templates = getAvailableTemplates();
  
  if (templates.length === 0) {
    console.error('❌ No templates found in src/templates directory');
    process.exit(1);
  }
  
  console.log(`🚀 Building ${templates.length} templates: ${templates.join(', ')}`);
  
  let successCount = 0;
  let failCount = 0;
  
  for (const templateId of templates) {
    if (buildTemplate(templateId)) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  console.log(`\n📊 Build Summary:`);
  console.log(`✅ Successful: ${successCount}`);
  console.log(`❌ Failed: ${failCount}`);
  
  if (failCount > 0) {
    process.exit(1);
  }
  
  console.log(`\n🎉 All templates built successfully!`);
}

main();
