#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get available templates by scanning the templates directory
function getAvailableTemplates() {
  const templatesDir = path.join(__dirname, '../src/templates');
  try {
    return fs.readdirSync(templatesDir)
      .filter(item => {
        const itemPath = path.join(templatesDir, item);
        return fs.statSync(itemPath).isDirectory();
      })
      .sort();
  } catch (error) {
    console.error('Error reading templates directory:', error);
    return [];
  }
}

// Get built templates by scanning the dist directory
function getBuiltTemplates() {
  const distDir = path.join(__dirname, '../dist');
  try {
    if (!fs.existsSync(distDir)) {
      return [];
    }
    
    return fs.readdirSync(distDir)
      .filter(item => {
        const itemPath = path.join(distDir, item);
        const indexPath = path.join(itemPath, 'index.html');
        return fs.statSync(itemPath).isDirectory() && fs.existsSync(indexPath);
      })
      .map(item => item.replace('template', ''))
      .sort();
  } catch (error) {
    console.error('Error reading dist directory:', error);
    return [];
  }
}

// Start preview server for a specific template
function startPreview(templateId) {
  const distPath = `dist/template${templateId}`;
  
  if (!fs.existsSync(distPath)) {
    console.error(`❌ Template ${templateId} not built yet.`);
    console.log(`💡 Build it first: npm run build:template ${templateId}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(`${distPath}/index.html`)) {
    console.error(`❌ Template ${templateId} missing index.html file.`);
    console.log(`💡 Try rebuilding: npm run build:template ${templateId}`);
    process.exit(1);
  }
  
  console.log(`🔍 Starting preview server for template ${templateId}...`);
  
  const child = spawn('npx', ['vite', 'preview'], {
    stdio: 'inherit',
    env: { 
      ...process.env, 
      TEMPLATE_TYPE: `template${templateId}` 
    }
  });
  
  child.on('close', (code) => {
    console.log(`Preview server exited with code ${code}`);
  });
  
  // Handle Ctrl+C gracefully
  process.on('SIGINT', () => {
    console.log('\n👋 Shutting down preview server...');
    child.kill('SIGINT');
    process.exit(0);
  });
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  const templates = getAvailableTemplates();
  const builtTemplates = getBuiltTemplates();
  
  if (templates.length === 0) {
    console.error('❌ No templates found in src/templates directory');
    process.exit(1);
  }
  
  let templateId;
  
  if (args.length === 0) {
    if (builtTemplates.length === 0) {
      console.error('❌ No built templates found.');
      console.log('💡 Build templates first: npm run build');
      process.exit(1);
    }
    
    // Default to first built template if none specified
    templateId = builtTemplates[0];
    console.log(`📋 Available templates: ${templates.join(', ')}`);
    console.log(`🏗️  Built templates: ${builtTemplates.join(', ')}`);
    console.log(`🎯 No template specified, using default: ${templateId}`);
    console.log(`💡 To specify a template: npm run preview <template_id>`);
  } else {
    templateId = args[0];
    
    if (!templates.includes(templateId)) {
      console.error(`❌ Template "${templateId}" not found.`);
      console.log('📋 Available templates:', templates.join(', '));
      process.exit(1);
    }
    
    if (!builtTemplates.includes(templateId)) {
      console.error(`❌ Template "${templateId}" not built yet.`);
      console.log(`💡 Build it first: npm run build:template ${templateId}`);
      process.exit(1);
    }
  }
  
  startPreview(templateId);
}

main();
