#!/bin/bash

# Build all templates
echo "Building Template 1 (Tailwind CSS)..."
npm run build:template1

echo "Building Template 2 (CSS Modules)..."
npm run build:template2

echo "Building Template 3 (Inline Styles)..."
npm run build:template3

echo "All templates built successfully!"
echo ""
echo "Build outputs:"
echo "- Template 1: dist/template1/"
echo "- Template 2: dist/template2/"
echo "- Template 3: dist/template3/"
echo ""
echo "Each template can be deployed to a different CDN path or subdomain."
