#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get available templates by scanning the templates directory
function getAvailableTemplates() {
  const templatesDir = path.join(__dirname, '../src/templates');
  try {
    return fs.readdirSync(templatesDir)
      .filter(item => {
        const itemPath = path.join(templatesDir, item);
        return fs.statSync(itemPath).isDirectory();
      })
      .sort();
  } catch (error) {
    console.error('Error reading templates directory:', error);
    return [];
  }
}

// Start development server for a specific template
function startDev(templateId) {
  console.log(`🚀 Starting development server for template ${templateId}...`);
  
  const child = spawn('npx', ['vite'], {
    stdio: 'inherit',
    env: { 
      ...process.env, 
      TEMPLATE_TYPE: `template${templateId}` 
    }
  });
  
  child.on('close', (code) => {
    console.log(`Development server exited with code ${code}`);
  });
  
  // Handle Ctrl+C gracefully
  process.on('SIGINT', () => {
    console.log('\n👋 Shutting down development server...');
    child.kill('SIGINT');
    process.exit(0);
  });
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  const templates = getAvailableTemplates();
  
  if (templates.length === 0) {
    console.error('❌ No templates found in src/templates directory');
    process.exit(1);
  }
  
  let templateId;
  
  if (args.length === 0) {
    // Default to first template if none specified
    templateId = templates[0];
    console.log(`📋 Available templates: ${templates.join(', ')}`);
    console.log(`🎯 No template specified, using default: ${templateId}`);
    console.log(`💡 To specify a template: npm run dev <template_id>`);
  } else {
    templateId = args[0];
    
    if (!templates.includes(templateId)) {
      console.error(`❌ Template "${templateId}" not found.`);
      console.log('📋 Available templates:', templates.join(', '));
      process.exit(1);
    }
  }
  
  startDev(templateId);
}

main();
