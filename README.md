# Dynamic Build System

A React-based build system that generates separate applications for different templates with various styling approaches. Each template is built as a standalone application with the same UI but different underlying HTML structure and styling systems.

## Features

- **Multiple Template Support**: Build separate applications for different component templates
- **Flexible Styling Systems**: Support for Tailwind CSS, CSS Modules, and Inline Styles
- **TypeScript**: Full type safety throughout the application
- **CSR Rendering**: Client-side rendering with template-specific builds
- **Configurable Themes**: Dynamic theming based on build configuration
- **Separate Build Outputs**: Each template builds to its own dist folder

## Template Structure

```
src/
├── templates/
│   ├── 1/                    # Tailwind CSS Template
│   │   ├── layout.tsx
│   │   └── homepage.tsx
│   ├── 2/                    # CSS Modules Template
│   │   ├── layout.tsx
│   │   ├── layout.module.css
│   │   ├── homepage.tsx
│   │   └── homepage.module.css
│   └── 3/                    # Inline Styles Template
│       ├── layout.tsx
│       └── homepage.tsx
├── data/                     # Single data entry point
│   └── pageData.ts
├── types/                    # TypeScript definitions
│   └── index.ts
├── utils/                    # Utility functions
│   ├── buildConfig.ts
│   └── dynamicLoader.tsx
├── App.tsx                   # Single unified App component
└── main.tsx                  # Single entry point
```

## Available Templates

### Template 1 - Tailwind CSS

- Modern design with Tailwind CSS utility classes
- Responsive layout with mobile-first approach
- Dynamic theming with CSS custom properties

### Template 2 - CSS Modules

- Classic design using CSS Modules for scoped styling
- Traditional CSS approach with modular components
- Maintainable and scalable styling

### Template 3 - Inline Styles

- Dynamic styling with React inline styles
- Runtime theming capabilities
- Maximum flexibility for dynamic customization

## Getting Started

1. Install dependencies:

```bash
npm install
```

2. Start the development server for a specific template:

```bash
# Template 1 (Tailwind CSS)
npm run dev:1

# Template 2 (CSS Modules)
npm run dev:2

# Template 3 (Inline Styles)
npm run dev:3
```

3. Open your browser and navigate to the provided URL

4. Each template will show the same UI but with different underlying HTML structure and styling systems

## Usage

The system works by:

1. **Single Entry Point**: All templates use the same `src/main.tsx` entry point
2. **Dynamic Template Loading**: Templates are loaded dynamically based on environment variables
3. **Build-Time Selection**: Templates are selected at build time using `TEMPLATE_TYPE` environment variable
4. **Separate Builds**: Each template builds to its own dist folder with optimized bundles
5. **Unified Data Layer**: Single data entry point in `src/data/pageData.ts` serves all templates

## Building Templates

Build all templates:

```bash
npm run build
```

Build specific template:

```bash
npm run build:1  # Tailwind CSS
npm run build:2  # CSS Modules
npm run build:3  # Inline Styles
```

Build outputs will be in:

- `dist/template1/` - Tailwind CSS template
- `dist/template2/` - CSS Modules template
- `dist/template3/` - Inline Styles template

## Adding New Templates

1. Create a new template folder in `src/templates/[templateId]/`
2. Implement `layout.tsx` and `homepage.tsx` components
3. Add the template configuration to `buildConfig.ts`
4. Update the `BuildType` union type in `types/index.ts`

## Customization

- **Themes**: Modify theme colors and fonts in the build configuration
- **Styling**: Each template can use its preferred styling approach
- **Components**: Add new page components following the existing pattern
- **Data**: Extend the data layer to support more dynamic content

## Build

```bash
npm run build
```

## Preview

```bash
npm run preview
```

## Technologies Used

- React 18
- TypeScript
- Vite
- Tailwind CSS
- CSS Modules
- React Router DOM
