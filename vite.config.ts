import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import { vanillaExtractPlugin } from "@vanilla-extract/vite-plugin";

// Get template type from environment variable or default to template1
const templateType = process.env.TEMPLATE_TYPE || "template1";

// Map template type to template ID for the App component
const templateId = templateType.replace("template", "");

export default defineConfig({
  plugins: [
    react(),
    vanillaExtractPlugin(),
    // SPA fallback plugin for template index.html
    {
      name: "spa-template-fallback",
      configureServer(server) {
        server.middlewares.use((req, res, next) => {
          if (
            req.url &&
            // Do not rewrite Vite internal or static asset requests
            !req.url.startsWith("/@") &&
            !req.url.startsWith("/src/") &&
            !req.url.startsWith("/node_modules/") &&
            !req.url.startsWith("/vite.svg") &&
            !req.url.includes(`/public/${templateType}/index.html`) &&
            !req.url.split("/").pop().includes(".")
          ) {
            req.url = `/public/${templateType}/index.html`;
          }
          next();
        });
      },
    },
  ],
  root: ".",
  publicDir: "public",
  define: {
    "process.env.TEMPLATE_TYPE": JSON.stringify(templateId),
  },
  server: {
    open: true,
    middlewareMode: false,
  },
  build: {
    outDir: `dist/${templateType}`,
    emptyOutDir: true,
    rollupOptions: {
      input: resolve(__dirname, `public/${templateType}/index.html`),
      output: {
        // Ensure assets are placed in the correct location
        assetFileNames: "assets/[name]-[hash][extname]",
        chunkFileNames: "assets/[name]-[hash].js",
        entryFileNames: "assets/[name]-[hash].js",
      },
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
      "@templates": resolve(__dirname, "./src/templates"),
      "@data": resolve(__dirname, "./src/data"),
      "@types": resolve(__dirname, "./src/types"),
      "@utils": resolve(__dirname, "./src/utils"),
    },
  },
  css: {
    modules: {
      localsConvention: "camelCase",
    },
  },
});
