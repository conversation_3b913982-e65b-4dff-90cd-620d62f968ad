<!doctype html>
<html lang="en" class="scroll-smooth" data-theme="emerald">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#FB8EB7" />
    <meta name="robots" content="index, follow" />
    <meta name="author" content="视频网站" />
    <meta property="og:title" content="视频网站" />
    <meta property="og:description" content="高质量视频内容平台" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />

    <title>视频网站</title>
    <meta name="description" content="高质量视频内容平台" />

    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Tailwind-specific optimizations -->
    <style>
      .loading-spinner { animation: spin 1s linear infinite; }
      @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
    </style>
  </head>
  <body class="antialiased bg-gray-50 text-gray-900" data-template="tailwind">
    <!-- TEMPLATE 1: Tailwind CSS - Utility-first CSS framework with responsive design -->
    <!-- This template uses Tailwind classes for styling and includes ads functionality -->
    <!-- Features: Responsive grid, hover effects, gradient overlays, modern UI components -->
    <noscript>
      <div style="text-align: center; padding: 2rem; background: #fef2f2; color: #dc2626;">
        This application requires JavaScript to run properly.
      </div>
    </noscript>

    <!-- Loading indicator -->
    <div id="loading" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
      <div class="loading-spinner w-8 h-8 border-4 border-emerald-500 border-t-transparent rounded-full"></div>
    </div>

    <div id="root" class="min-h-screen"></div>

    <script>
      // Hide loading indicator when React loads
      window.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) loading.style.display = 'none';
        }, 500);
      });
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
