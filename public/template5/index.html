<!DOCTYPE html>
<html lang="zh-CN" data-emotion="true" class="emotion-optimized">
<head>
  <!-- Essential Meta -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="format-detection" content="telephone=no, email=no">

  <!-- Document Info -->
  <title>视频网站</title>
  <meta name="description" content="高质量视频内容平台">
  <meta name="keywords" content="emotion, css-in-js, 视频平台, 高性能, 动态样式">
  <meta name="author" content="Dynamic Build Team">
  <meta name="generator" content="Emotion CSS Template">

  <!-- Branding and Theme -->
  <meta name="theme-color" content="#FB8EB7">
  <meta name="msapplication-navbutton-color" content="#FB8EB7">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-capable" content="yes">

  <!-- Social Media / Open Graph -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="视频网站 - Emotion CSS">
  <meta property="og:description" content="体验 Emotion 的高性能 CSS-in-JS 解决方案">
  <meta property="og:image" content="/emotion-preview.jpg">
  <meta property="og:locale" content="zh_CN">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@emotionjs">

  <!-- Icons and Favicons -->
  <link rel="icon" type="image/svg+xml" href="/vite.svg">
  <link rel="alternate icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#FB8EB7">

  <!-- Font Strategy - Optimized for Emotion -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

  <!-- Emotion CSS Variables and Loading Styles -->
  <style>
    :root {
      --emotion-primary: #FB8EB7;
      --emotion-secondary: #EAA6C7;
      --emotion-tertiary: #D9BFD9;
      --emotion-bg: #f9fafb;
      --emotion-surface: #ffffff;
      --emotion-text: #111827;
    }







    @media (prefers-color-scheme: dark) {
      .emotion-perf {
        background: rgba(17, 24, 39, 0.95);
        color: var(--emotion-secondary);
        border-color: rgba(234, 166, 199, 0.3);
      }
    }
  </style>
</head>

<body data-template="emotion" data-css-in-js="emotion" style="margin: 0; padding: 0; font-family: 'Noto Sans SC', sans-serif; background: var(--emotion-bg);">
  <!-- TEMPLATE 5: Emotion CSS - High-performance CSS-in-JS with runtime optimization -->
  <!-- This template uses Emotion for CSS-in-JS styling and includes ads functionality -->
  <!-- Features: Emotion CSS-in-JS, performance optimization, runtime styles, SSR support, development tools -->
  <!-- Main App Container -->
  <div id="root" role="main" aria-label="Emotion CSS Application"></div>


  <!-- Initialization Scripts -->
  <script>
    // Performance tracking
    window.emotionStartTime = performance.now();
    console.log('⚡ Emotion CSS Template starting...');
    console.log('🎨 CSS-in-JS Engine: Emotion');
    console.log('📊 Performance Mode: Optimized');

    // Error boundary
    window.addEventListener('error', function(e) {
      console.error('Emotion Template Error:', e.error);
      const errorDiv = document.getElementById('emotion-error');
      if (errorDiv) errorDiv.style.display = 'flex';
    });



    // Development helpers
    if (process?.env?.NODE_ENV === 'development') {
      console.log('🔧 Emotion DevTools: Available');
    }
  </script>

  <!-- Main Application -->
  <script type="module" src="/src/main.tsx"></script>

  <!-- Post-load Analytics -->
  <script>
    window.addEventListener('load', () => {
      const loadTime = performance.now() - window.emotionStartTime;
      console.log(`⚡ Emotion Template loaded in ${loadTime.toFixed(2)}ms`);



      // Cleanup and optimization
      setTimeout(() => {
        if (typeof window.gc === 'function') {
          window.gc();
        }
      }, 5000);
    });
  </script>
</body>
</html>
