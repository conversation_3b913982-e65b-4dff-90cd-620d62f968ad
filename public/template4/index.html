<!doctype html>
<html lang="en" data-framework="styled-components" class="theme-provider-enabled">
  <head>
    <!-- Core Meta Tags -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="color-scheme" content="light dark" />

    <!-- SEO and Branding -->
    <title>视频网站</title>
    <meta name="description" content="高质量视频内容平台" />
    <meta name="keywords" content="styled-components, css-in-js, 视频, 在线观看" />
    <meta name="theme-color" content="#FB8EB7" />
    <meta name="msapplication-TileColor" content="#FB8EB7" />

    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="视频网站 - Styled Components" />
    <meta property="og:description" content="体验 CSS-in-JS 的强大功能" />
    <meta property="og:site_name" content="Dynamic Build System" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:creator" content="@dynamicbuild" />

    <!-- Icons and Manifest -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Font Loading Strategy -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet" />

    <!-- Styled Components Theme Variables -->
    <style>
      :root {
        --sc-primary: #FB8EB7;
        --sc-secondary: #EAA6C7;
        --sc-tertiary: #D9BFD9;
        --sc-surface: #ffffff;
        --sc-background: #f9fafb;
      }




    </style>
  </head>
  <body data-template="styled-components" data-version="4.0" style="margin: 0; font-family: 'Roboto', sans-serif;">
    <!-- TEMPLATE 4: Styled Components - CSS-in-JS with component-based styling -->
    <!-- This template uses styled-components for dynamic styling and includes ads functionality -->
    <!-- Features: CSS-in-JS, theme provider, dynamic styles, component isolation, performance optimization -->
    <!-- Main Application Root -->
    <div id="root" style="min-height: 100vh;"></div>



    <!-- Performance and Analytics -->
    <script>
      // Performance monitoring
      window.styledComponentsStartTime = performance.now();
      console.log('💅 Styled Components Template initializing...');

      // Theme system logging
      console.log('🎨 Theme Provider: Enabled');
      console.log('🎯 CSS-in-JS: Active');

      // Error handling
      window.addEventListener('error', function(e) {
        console.error('Styled Components Error:', e.error);
        document.getElementById('sc-error-boundary').style.display = 'block';
      });


    </script>

    <!-- Main Application -->
    <script type="module" src="/src/main.tsx"></script>

    <!-- Post-load optimizations -->
    <script>
      window.addEventListener('load', () => {
        const loadTime = performance.now() - window.styledComponentsStartTime;
        console.log(`💅 Styled Components Template loaded in ${loadTime.toFixed(2)}ms`);

        // Analytics event
        if (typeof gtag !== 'undefined') {
          gtag('event', 'template_load', {
            template_type: 'styled-components',
            load_time: loadTime
          });
        }
      });
    </script>
  </body>
</html>
