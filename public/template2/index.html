<!doctype html>
<html lang="en" dir="ltr" class="classic-theme">
  <head>
    <!-- Essential Meta Tags -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <!-- SEO and Social Meta -->
    <title>视频网站</title>
    <meta name="description" content="高质量视频内容平台" />
    <meta name="keywords" content="视频, 在线观看, 高清, 娱乐" />
    <meta name="generator" content="视频网站" />
    <meta property="og:title" content="视频网站" />
    <meta property="og:description" content="高质量视频内容平台" />
    <meta property="og:image" content="/og-image.jpg" />
    <link rel="canonical" href="https://video-site.example.com" />

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />

    <!-- Fonts with different loading strategy -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Georgia:wght@400;700&display=swap" as="style" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
    <link href="https://fonts.googleapis.com/css2?family=Georgia:wght@400;700&display=swap" rel="stylesheet" />

    <!-- CSS Modules specific styles -->
    <style>
      :root {
        --primary-hue: 158;
        --primary-saturation: 64%;
        --primary-lightness: 52%;
      }
      .css-modules-root {
        font-family: Georgia, 'Times New Roman', serif;
        line-height: 1.6;
      }

      /* Accessibility skip link styles */
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #10B981;
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
        font-size: 14px;
        font-weight: 600;
        transition: top 0.3s ease;
      }

      .skip-link:focus {
        top: 6px;
      }
    </style>
  </head>
  <body class="css-modules-root" data-template="css-modules" data-version="2.0">
    <!-- TEMPLATE 2: CSS Modules - Scoped CSS with class-based styling -->
    <!-- This template uses CSS modules for component-scoped styles and includes ads functionality -->
    <!-- Features: Scoped styles, accessibility features, performance monitoring, responsive design -->
    <!-- Accessibility skip link -->
    <a href="#main-content" class="skip-link">
      Skip to main content
    </a>

    <div id="root" role="application" aria-label="Dynamic Build System Application"></div>

    <!-- Performance monitoring -->
    <script>
      window.performance.mark('template2-start');
      console.log('🎨 CSS Modules Template Loading...');
    </script>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      window.addEventListener('load', () => {
        window.performance.mark('template2-end');
        console.log('✅ CSS Modules Template Loaded');
      });
    </script>
  </body>
</html>
