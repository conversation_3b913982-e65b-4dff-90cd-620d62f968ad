<!DOCTYPE html>
<html lang="en" data-styling="inline" class="dynamic-styles-enabled">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
  <meta name="format-detection" content="telephone=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">

  <!-- Site title -->
  <title>视频网站</title>

  <!-- Enhanced meta descriptions -->
  <meta name="description" content="高质量视频内容平台">
  <meta name="keywords" content="视频, 在线观看, 高清, 娱乐">
  <meta name="application-name" content="视频网站">

  <!-- Social sharing optimized -->
  <meta property="og:site_name" content="视频网站">
  <meta property="og:title" content="视频网站">
  <meta property="og:description" content="高质量视频内容平台">
  <meta property="og:url" content="https://video-site.example.com">
  <meta name="twitter:title" content="视频网站">
  <meta name="twitter:description" content="高质量视频内容平台">

  <!-- Favicon variations -->
  <link rel="icon" type="image/svg+xml" href="/vite.svg">
  <link rel="shortcut icon" href="/favicon.ico">
  <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#10B981">

  <!-- Font loading with different strategy -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Arial:wght@400;600;700&display=swap" rel="stylesheet">

  <!-- Inline styles for immediate rendering -->
  <style>
    * { box-sizing: border-box; }
    html { scroll-behavior: smooth; }
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
      transition: all 0.3s ease;
    }
    .inline-styles-container {
      min-height: 100vh;
      position: relative;
    }
    .theme-indicator {
      position: fixed;
      top: 10px;
      right: 10px;
      background: #10B981;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 1000;
      opacity: 0.8;
    }
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after { animation-duration: 0.01ms !important; }
    }
  </style>
</head>

<body data-template="inline-styles" data-build="3" style="overflow-x: hidden;">
  <!-- TEMPLATE 3: Inline Styles - CSS-in-HTML with embedded styles -->
  <!-- This template uses inline styles and embedded CSS for styling and includes ads functionality -->
  <!-- Features: Inline styles, embedded CSS, error boundaries, theme indicators, performance tracking -->
  <!-- Main application container -->
  <div id="root" class="inline-styles-container" role="main" aria-live="polite"></div>

  <!-- Error boundary fallback -->
  <div id="error-fallback" style="display: none; text-align: center; padding: 2rem; background: #fef2f2; color: #dc2626; border: 1px solid #fecaca; margin: 1rem; border-radius: 8px;">
    <h2>⚠️ Something went wrong</h2>
    <p>Please refresh the page or contact support.</p>
    <button onclick="location.reload()" style="background: #dc2626; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
      Refresh Page
    </button>
  </div>

  <!-- Analytics and performance tracking -->
  <script>
    // Performance monitoring
    window.templateStartTime = performance.now();
    console.log('🎨 Inline Styles Template initializing...');

    // Error handling
    window.addEventListener('error', function(e) {
      console.error('Template 3 Error:', e.error);
      document.getElementById('error-fallback').style.display = 'block';
    });

    // Theme indicator animation
    setTimeout(() => {
      const indicator = document.getElementById('theme-indicator');
      if (indicator) {
        indicator.style.transform = 'translateX(-10px)';
        setTimeout(() => {
          indicator.style.opacity = '0.6';
        }, 2000);
      }
    }, 1000);
  </script>

  <!-- Main application script -->
  <script type="module" src="/src/main.tsx"></script>

  <!-- Post-load optimizations -->
  <script>
    window.addEventListener('load', () => {
      const loadTime = performance.now() - window.templateStartTime;
      console.log(`✨ Inline Styles Template loaded in ${loadTime.toFixed(2)}ms`);

      // Remove theme indicator after 5 seconds
      setTimeout(() => {
        const indicator = document.getElementById('theme-indicator');
        if (indicator) {
          indicator.style.transition = 'opacity 0.5s ease';
          indicator.style.opacity = '0';
          setTimeout(() => indicator.remove(), 500);
        }
      }, 5000);
    });
  </script>
</body>
</html>
