<!DOCTYPE html>
<html lang="en" data-css="vanilla-extract" class="zero-runtime">
<head>
  <!-- Core Meta -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="referrer" content="strict-origin-when-cross-origin">

  <!-- Document Meta -->
  <title>视频网站</title>
  <meta name="description" content="高质量视频内容平台">
  <meta name="keywords" content="vanilla-extract, zero-runtime, css-in-ts, 视频, 类型安全">
  <meta name="application-name" content="视频网站">
  <meta name="generator" content="Vanilla Extract CSS">

  <!-- Theme and Branding -->
  <meta name="theme-color" content="#FB8EB7">
  <meta name="msapplication-TileColor" content="#FB8EB7">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="视频网站">

  <!-- Social / Open Graph -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="视频网站">
  <meta property="og:description" content="高质量视频内容平台">
  <meta property="og:site_name" content="Vanilla Extract Template">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="视频网站">
  <meta name="twitter:description" content="零运行时 CSS-in-TypeScript">

  <!-- Icons -->
  <link rel="icon" type="image/svg+xml" href="/vite.svg">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;500;600&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">

  <!-- Vanilla Extract CSS Variables -->
  <style>
    :root {
      --ve-primary: #FB8EB7;
      --ve-secondary: #EAA6C7;
      --ve-tertiary: #D9BFD9;
      --ve-bg: #f9fafb;
      --ve-surface: #ffffff;
      --ve-text: #111827;
      --ve-border: #e5e7eb;
    }





    /* Performance optimizations */
    * {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      font-family: 'Inter', sans-serif;
      background: var(--ve-bg);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }


  </style>
</head>

<body data-template="vanilla-extract" data-runtime="zero" data-type-safe="true">
  <!-- TEMPLATE 6: Vanilla Extract - Zero-runtime CSS-in-TypeScript with type safety -->
  <!-- This template uses Vanilla Extract for zero-runtime CSS and includes ads functionality -->
  <!-- Features: Zero-runtime CSS, TypeScript integration, type safety, build-time optimization, static extraction -->
  <!-- Main Application -->
  <div id="root"></div>



  <!-- Scripts -->
  <script>
    window.vanillaExtractStartTime = performance.now();
    console.log('🍦 Vanilla Extract Template initializing...');
    console.log('⚡ Runtime: Zero');
    console.log('🔒 Type Safety: Enabled');

    window.addEventListener('error', function(e) {
      console.error('Vanilla Extract Error:', e.error);
      document.getElementById('ve-error').style.display = 'flex';
    });


  </script>

  <script type="module" src="/src/main.tsx"></script>

  <script>
    window.addEventListener('load', () => {
      const loadTime = performance.now() - window.vanillaExtractStartTime;
      console.log(`🍦 Vanilla Extract loaded in ${loadTime.toFixed(2)}ms`);
    });
  </script>
</body>
</html>
